"""Enhanced Local AI Assistant with multi-model support and performance optimizations."""

import sys
import os
import threading
import time
from enhanced_config import WELCOME_MESSAGE, CONNECTION_SUCCESS, CONNECTION_FAILED
from enhanced_gui import EnhancedG<PERSON>
from enhanced_ollama_api import <PERSON>hanced<PERSON>llamaAP<PERSON>
from ai_assistant import AIAssistant


class EnhancedLocalAIAssistant:
    """Enhanced main application with multi-model support."""
    
    def __init__(self):
        try:
            self.gui = EnhancedGUI()
            self.ollama = EnhancedOllamaAPI()
            self.assistant = AIAssistant()

            # Replace assistant's ollama client with enhanced version
            self.assistant.ollama = self.ollama

            self.setup_callbacks()
            self.initialize_app()

        except Exception as e:
            self.handle_startup_error(e)
    
    def handle_startup_error(self, error):
        """Handle startup errors gracefully."""
        print(f"❌ Startup Error: {str(error)}")
        
        if "tkinter" in str(error).lower() or "tcl" in str(error).lower():
            print("\n🔧 GUI Error Detected - Offering Solutions:")
            print("1. Try the console version: python console_version.py")
            print("2. Run outside virtual environment")
            print("3. Install tkinter system-wide")
            
            # Try to create console version
            try:
                choice = input("\nWould you like to start the console version instead? (y/n): ").lower().strip()
                if choice == 'y':
                    self.start_console_version()
                    return
            except:
                pass
        
        sys.exit(1)
    
    def start_console_version(self):
        """Start console version as fallback."""
        try:
            from console_version import ConsoleAIAssistant
            console_app = ConsoleAIAssistant()
            console_app.run()
        except Exception as e:
            print(f"❌ Console version also failed: {e}")
            sys.exit(1)
    
    def setup_callbacks(self):
        """Set up GUI callbacks."""
        self.gui.set_send_callback(self.handle_send_message)
        self.gui.set_browse_callback(self.handle_browse_file)
        self.gui.set_model_change_callback(self.handle_model_change)
        self.gui.set_download_callback(self.handle_model_download)

        # Session management callbacks
        self.gui.new_chat_callback = self.handle_new_chat
        self.gui.load_chat_callback = self.handle_load_chat

        # Multimodal callbacks
        self.gui.analyze_image_callback = self.handle_analyze_image
        self.gui.generate_image_callback = self.handle_generate_image
        self.gui.screenshot_callback = self.handle_screenshot

        # Voice and autonomous callbacks
        self.gui.voice_toggle_callback = self.handle_voice_toggle
        self.gui.autonomous_task_callback = self.handle_autonomous_task

        # Handle window close
        self.gui.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def initialize_app(self):
        """Initialize the application."""
        # Show welcome message
        self.gui.add_message("System", WELCOME_MESSAGE, "system")
        
        # Test Ollama connection and get models
        self.test_connection_and_models()
    
    def test_connection_and_models(self):
        """Test connection and load available models."""
        def _test():
            success, message = self.ollama.test_connection()
            
            # Update GUI on main thread
            self.gui.root.after(0, lambda: self._update_connection_status(success, message))
        
        threading.Thread(target=_test, daemon=True).start()
    
    def _update_connection_status(self, success: bool, message: str):
        """Update connection status and models in GUI."""
        status_msg = CONNECTION_SUCCESS if success else CONNECTION_FAILED
        self.gui.update_connection_status(f"{status_msg}: {message}", success)
        
        if success:
            # Update available models
            models = self.ollama.get_available_models()
            self.gui.update_models(models)
            
            # Set current model in assistant and GUI
            current_model = self.ollama.get_current_model()
            if hasattr(self.gui, 'current_model'):
                self.gui.current_model = current_model
            
            self.gui.add_message("System", 
                f"✅ Connected! Using model: {current_model}\n"
                f"📋 {len(models)} models available. Switch models using the sidebar.", 
                "system")
        else:
            self.gui.add_message("System", 
                f"⚠️ Connection Issue: {message}\n\n"
                "Please make sure:\n"
                "1. Ollama is installed and running (ollama serve)\n"
                "2. At least one model is downloaded (ollama pull phi3:mini)\n"
                "3. Ollama is accessible on localhost:11434", 
                "error")
    
    def handle_send_message(self):
        """Handle sending a message."""
        user_input = self.gui.get_input_text()
        if not user_input.strip():
            return
        
        # Add user message to chat
        current_model = self.ollama.get_current_model()
        self.gui.add_message("You", user_input, "user")
        self.gui.clear_input()
        
        # Disable input and show typing indicator
        self.gui.set_input_enabled(False)
        self.gui.show_typing_indicator()
        self.gui.update_status(f"AI ({current_model}) is thinking...")
        
        # Process with AI assistant using current model
        self.assistant.process_user_input(
            user_input,
            self.on_ai_response,
            self.on_ai_error
        )
    
    def on_ai_response(self, ai_response: str, file_operations: list = None):
        """Handle AI response."""
        def _handle():
            try:
                # Hide typing indicator and re-enable input
                self.gui.hide_typing_indicator()
                self.gui.set_input_enabled(True)
                self.gui.update_status("Ready")
                
                # Add AI response to chat
                current_model = self.ollama.get_current_model()
                self.gui.add_message(f"AI ({current_model})", ai_response, "ai")
                
                # Handle file operations if any
                if file_operations:
                    self.handle_file_operations(file_operations)
                    
            except Exception as e:
                self.gui.add_message("System", f"Error processing response: {str(e)}", "error")
                self.gui.set_input_enabled(True)
                self.gui.update_status("Error")
        
        # Execute on main thread
        self.gui.root.after(0, _handle)
    
    def on_ai_error(self, error_message: str):
        """Handle AI error."""
        def _handle():
            self.gui.hide_typing_indicator()
            self.gui.set_input_enabled(True)
            self.gui.update_status("Error")
            self.gui.add_message("System", f"❌ Error: {error_message}", "error")
        
        # Execute on main thread
        self.gui.root.after(0, _handle)
    
    def handle_file_operations(self, file_operations: list):
        """Handle file operations from AI response."""
        for operation in file_operations:
            try:
                success, message = self.assistant.execute_file_operation(operation)
                
                if success:
                    self.gui.add_message("System", f"✅ {message}", "success")
                else:
                    self.gui.add_message("System", f"❌ {message}", "error")
                    
            except Exception as e:
                self.gui.add_message("System", 
                    f"❌ Error executing file operation: {str(e)}", "error")
    
    def handle_browse_file(self):
        """Handle file browsing."""
        try:
            success, message, file_path = self.assistant.browse_and_load_file()
            
            if success:
                self.gui.add_message("System", f"📁 {message}", "system")
                self.gui.update_status(f"Loaded: {os.path.basename(file_path)}")
            else:
                if "No file selected" not in message:
                    self.gui.add_message("System", f"❌ {message}", "error")
                    
        except Exception as e:
            self.gui.add_message("System", f"❌ Error browsing file: {str(e)}", "error")
    
    def handle_model_change(self, model_name: str):
        """Handle model change with session management."""
        try:
            # Switch to model using session manager
            session_id = self.assistant.switch_model(model_name)

            # Update Ollama model
            success, message = self.ollama.set_model(model_name)

            if success:
                # Update session info in GUI
                session_info = self.assistant.get_current_session_info()
                if session_info:
                    self.gui.update_session_info(session_info)

                self.gui.add_message("System", f"🔄 Switched to model: {model_name}", "system")
                self.gui.update_status(f"Model: {model_name}")
            else:
                self.gui.add_message("System", f"❌ Failed to switch model: {message}", "error")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error switching model: {str(e)}", "error")

    def handle_new_chat(self):
        """Handle new chat creation."""
        try:
            # Get current model
            current_model = self.ollama.current_model

            # Create new session
            session_id = self.assistant.create_new_chat(current_model, f"New Chat - {current_model}")

            # Clear chat display
            self.gui.clear_chat()

            # Update session info
            session_info = self.assistant.get_current_session_info()
            if session_info:
                self.gui.update_session_info(session_info)

            # Show welcome message for new chat
            self.gui.add_message("System", f"✨ Started new chat with {current_model}", "system")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error creating new chat: {str(e)}", "error")

    def handle_load_chat(self):
        """Handle chat loading."""
        try:
            # Get all sessions
            sessions = self.assistant.get_all_sessions()

            if not sessions:
                self.gui.add_message("System", "📭 No saved chats found", "system")
                return

            # Create simple selection dialog
            session_list = "\n".join([f"{i+1}. {s['summary']}" for i, s in enumerate(sessions[:10])])

            self.gui.add_message("System", f"📚 Available chats:\n{session_list}\n\n💡 Use 'load chat [number]' to load a specific chat", "system")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error loading chats: {str(e)}", "error")

    def handle_analyze_image(self):
        """Handle image analysis request."""
        try:
            from multimodal_handler import multimodal_handler
            success, result = multimodal_handler.browse_and_analyze_image()

            if success:
                self.gui.add_message("System", result, "system")
            else:
                self.gui.add_message("System", f"❌ Image analysis failed: {result}", "error")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error analyzing image: {str(e)}", "error")

    def handle_generate_image(self, prompt: str):
        """Handle image generation request."""
        try:
            from multimodal_handler import multimodal_handler

            self.gui.add_message("System", f"🎨 Generating image: {prompt}", "system")
            success, result = multimodal_handler.generate_image_stable_diffusion(prompt)

            if success:
                self.gui.add_message("System", result, "system")
            else:
                self.gui.add_message("System", f"❌ Image generation failed: {result}", "error")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error generating image: {str(e)}", "error")

    def handle_screenshot(self):
        """Handle screenshot request."""
        try:
            from multimodal_handler import multimodal_handler
            success, result = multimodal_handler.capture_screen()

            if success:
                self.gui.add_message("System", result, "system")
            else:
                self.gui.add_message("System", f"❌ Screenshot failed: {result}", "error")

        except Exception as e:
            self.gui.add_message("System", f"❌ Error taking screenshot: {str(e)}", "error")

    def handle_voice_toggle(self):
        """Handle voice mode toggle."""
        try:
            voice_status = self.assistant.get_voice_status()

            if not voice_status.get('available', False):
                self.gui.add_message("System", "❌ Voice features not available. Install dependencies: pip install speechrecognition pyttsx3 pyaudio", "error")
                return

            if voice_status.get('enabled', False):
                # Disable voice mode
                self.assistant.disable_voice_mode()
                self.gui.add_message("System", "🔇 Voice mode disabled", "system")
                self.gui.update_voice_button_status(False, False)
            else:
                # Enable voice mode
                success = self.assistant.enable_voice_mode(continuous=True)
                if success:
                    self.gui.add_message("System", "🎤 Voice mode enabled. Say 'Hey Assistant' to get my attention.", "system")
                    self.gui.update_voice_button_status(True, False)
                else:
                    self.gui.add_message("System", "❌ Failed to enable voice mode", "error")

        except Exception as e:
            self.gui.add_message("System", f"❌ Voice toggle failed: {str(e)}", "error")

    def handle_autonomous_task(self, task_description: str):
        """Handle autonomous task execution."""
        try:
            if not task_description or not task_description.strip():
                return

            self.gui.add_message("System", f"🤖 Starting autonomous task: {task_description}", "system")
            self.gui.update_autonomous_button_status(True)

            # Execute the autonomous task
            success = self.assistant.execute_autonomous_task(task_description)

            if success:
                self.gui.add_message("System", "✅ Autonomous task started successfully", "system")
            else:
                self.gui.add_message("System", "❌ Failed to start autonomous task", "error")
                self.gui.update_autonomous_button_status(False)

        except Exception as e:
            self.gui.add_message("System", f"❌ Autonomous task failed: {str(e)}", "error")
            self.gui.update_autonomous_button_status(False)
    
    def handle_model_download(self, model_name: str):
        """Handle model download request."""
        self.gui.add_message("System", f"📥 Starting download of {model_name}...", "system")
        self.gui.update_status(f"Downloading {model_name}...")
        
        def on_progress(progress_msg):
            self.gui.root.after(0, lambda: self.gui.update_status(progress_msg))
        
        def on_complete(success, message):
            def _complete():
                if success:
                    self.gui.add_message("System", f"✅ {message}", "success")
                    # Refresh models list
                    self.test_connection_and_models()
                else:
                    self.gui.add_message("System", f"❌ {message}", "error")
                self.gui.update_status("Ready")
            
            self.gui.root.after(0, _complete)
        
        self.ollama.pull_model_async(model_name, on_progress, on_complete)
    
    def on_closing(self):
        """Handle application closing."""
        if self.gui.messagebox.askokcancel("Quit", "Exit the Local AI Assistant?"):
            self.gui.root.quit()
            self.gui.root.destroy()
    
    def run(self):
        """Start the application."""
        try:
            print("🚀 Starting Enhanced Local AI Assistant...")
            self.gui.root.mainloop()
            print("👋 Application closed")
        except KeyboardInterrupt:
            print("\n🛑 Application interrupted by user")
        except Exception as e:
            print(f"💥 Application error: {str(e)}")
            self.gui.messagebox.showerror("Application Error", 
                f"An unexpected error occurred:\n{str(e)}")


def main():
    """Main entry point."""
    try:
        app = EnhancedLocalAIAssistant()
        app.run()
    except Exception as e:
        print(f"💥 Failed to start application: {str(e)}")
        
        # Offer console version as fallback
        try:
            choice = input("\nWould you like to try the console version? (y/n): ").lower().strip()
            if choice == 'y':
                from console_version import ConsoleAIAssistant
                console_app = ConsoleAIAssistant()
                console_app.run()
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
