"""Core AI assistant logic for processing requests and managing file operations."""

import re
import gc
from typing import List, Dict, Optional, <PERSON><PERSON>
from enhanced_config import MAX_CONVERSATION_HISTORY
from enhanced_ollama_api import EnhancedOllamaAP<PERSON>
from file_operations import FileOperations, CodeExtractor
from command_parser import parse_and_execute, is_pc_command, get_command_help
from chat_session_manager import session_manager

# Import new autonomous capabilities
try:
    from voice_interface import voice_interface, VoiceSettings
    from screen_automation import screen_automation, AutomationSettings
    from autonomous_agent import autonomous_agent, execute_template_task
    from model_coordinator import model_coordinator, delegate_task, TaskType
    from smart_delegation import smart_delegator, get_best_model_for_request
    AUTONOMOUS_AVAILABLE = True
except ImportError:
    AUTONOMOUS_AVAILABLE = False
    voice_interface = None
    screen_automation = None
    autonomous_agent = None
    model_coordinator = None


class AIAssistant:
    """Main AI assistant that coordinates between GUI, API, and file operations."""
    
    def __init__(self):
        self.ollama = EnhancedOllamaAPI()
        self.file_ops = FileOperations()
        self.code_extractor = CodeExtractor()

        # Current context
        self.current_file_path = None
        self.current_file_content = None

        # Voice and autonomous capabilities
        self.voice_enabled = False
        self.autonomous_mode = False
        self.voice_listening = False

        # Initialize with default session if none exists
        if not session_manager.get_current_session():
            session_manager.create_new_session("phi3:mini", "Default Chat")

        # Initialize autonomous capabilities if available
        self._initialize_autonomous_features()

    def _initialize_autonomous_features(self):
        """Initialize voice and autonomous capabilities."""
        if not AUTONOMOUS_AVAILABLE:
            return

        try:
            # Set up voice interface callbacks
            voice_interface.on_speech_recognized = self._handle_voice_command
            voice_interface.on_wake_word_detected = self._handle_wake_word
            voice_interface.on_listening_started = self._handle_listening_started
            voice_interface.on_listening_stopped = self._handle_listening_stopped
            voice_interface.on_error = self._handle_voice_error

            # Set up autonomous agent callbacks
            autonomous_agent.on_task_started = self._handle_task_started
            autonomous_agent.on_task_completed = self._handle_task_completed
            autonomous_agent.on_task_failed = self._handle_task_failed
            autonomous_agent.on_confirmation_required = self._handle_confirmation_required

            print("✅ Autonomous features initialized successfully")

        except Exception as e:
            print(f"⚠️ Failed to initialize autonomous features: {str(e)}")

    def test_connection(self) -> Tuple[bool, str]:
        """Test connection to Ollama."""
        return self.ollama.test_connection()
    
    def add_to_history(self, role: str, content: str):
        """Add message to current session's conversation history."""
        session_manager.add_message_to_current(role, content)

    def clear_conversation(self):
        """Clear current session's conversation history."""
        session_manager.clear_current_session()

    def switch_model(self, model_name: str) -> str:
        """Switch to a different model, creating or resuming session."""
        session_id = session_manager.switch_model(model_name, create_new=True)
        return session_id

    def create_new_chat(self, model_name: str = None, session_name: str = None) -> str:
        """Create a new chat session."""
        if not model_name:
            current_session = session_manager.get_current_session()
            model_name = current_session.model_name if current_session else "phi3:mini"

        session_id = session_manager.create_new_session(model_name, session_name)
        return session_id

    def get_current_session_info(self) -> dict:
        """Get information about current session."""
        session = session_manager.get_current_session()
        if session:
            return {
                "session_id": session.session_id,
                "model_name": session.model_name,
                "session_name": session.session_name,
                "message_count": session.message_count,
                "created_at": session.created_at.strftime("%Y-%m-%d %H:%M"),
                "last_updated": session.last_updated.strftime("%Y-%m-%d %H:%M")
            }
        return None

    def get_all_sessions(self) -> list:
        """Get all available sessions."""
        sessions = session_manager.get_all_sessions()
        return [
            {
                "session_id": s.session_id,
                "model_name": s.model_name,
                "session_name": s.session_name,
                "message_count": s.message_count,
                "summary": s.get_summary()
            }
            for s in sessions
        ]
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def process_user_input(self, user_input: str,
                          on_response_callback,
                          on_error_callback,
                          use_streaming: bool = False):
        """
        Process user input with intelligent model delegation and autonomous capabilities.

        Args:
            user_input: User's message
            on_response_callback: Callback for AI response
            on_error_callback: Callback for errors
            use_streaming: Whether to use streaming for faster perceived response
        """
        # Add user input to history
        self.add_to_history("user", user_input)

        # Check if this is a PC interaction command
        if is_pc_command(user_input):
            self._handle_pc_command(user_input, on_response_callback, on_error_callback)
            return

        # Check for help request
        if any(word in user_input.lower() for word in ['help', 'commands', 'what can you do']):
            if 'pc' in user_input.lower() or 'computer' in user_input.lower() or 'system' in user_input.lower():
                help_text = get_command_help()
                self._handle_ai_response(help_text, user_input, on_response_callback)
                return

        # Use smart model delegation
        if AUTONOMOUS_AVAILABLE and smart_delegator:
            try:
                # Get the best model for this request
                selected_model, task_type = get_best_model_for_request(user_input)

                # Switch to the selected model if different from current
                current_model = self.ollama.get_current_model()
                if selected_model != current_model:
                    success, message = self.ollama.set_model(selected_model)
                    if success:
                        print(f"🎯 Using {selected_model} for {task_type} task")
                    else:
                        # Fallback to current model if switch fails
                        selected_model = current_model
                        print(f"⚠️ Failed to switch to {selected_model}, using {current_model}")

                # Build enhanced prompt for the task type
                prompt = self._build_enhanced_prompt(user_input, task_type)

            except Exception as e:
                # Fallback to regular processing if delegation fails
                prompt = self._build_prompt(user_input)
                print(f"Model delegation failed, using fallback: {str(e)}")
        else:
            # Build regular prompt if delegation not available
            prompt = self._build_prompt(user_input)

        # Optimize prompt length for faster responses
        if len(prompt) > 2000:
            prompt = prompt[:2000] + "..."

        if use_streaming:
            # Use streaming for faster perceived response time
            accumulated_response = ""

            def on_chunk(chunk):
                nonlocal accumulated_response
                accumulated_response += chunk
                # Could update GUI with partial response here

            def on_complete(full_response):
                self._handle_ai_response(full_response, user_input, on_response_callback)

            self.ollama.generate_streaming(
                prompt,
                on_chunk,
                on_complete,
                on_error_callback
            )
        else:
            # Use optimized async generation
            self.ollama.generate_async(
                prompt,
                lambda response: self._handle_ai_response(response, user_input, on_response_callback),
                on_error_callback,
                optimize_for_speed=True
            )
    
    def _build_prompt(self, user_input: str) -> str:
        """Build context-aware prompt for the AI (optimized)."""
        # Use a more concise system prompt for better performance
        system_prompt = """You are a helpful AI assistant with PC interaction capabilities. You can help with:

• Conversations, writing, analysis, problem-solving
• Programming and file creation
• PC interaction: running programs, executing commands, managing files
• System operations: file management, directory listing, program launching

When creating files, use properly formatted code blocks with triple backticks and language specification.

For PC interactions, users can use commands like:
- "open notepad" - Launch programs
- "create file example.txt" - Create files
- "list directory" - Show folder contents
- "run command dir" - Execute system commands

Be helpful, conversational, and adapt to the user's needs."""

        # Add current file context if available (truncated for performance)
        context = ""
        if self.current_file_path and self.current_file_content:
            # Limit context size for better performance
            content_preview = self.current_file_content[:500]
            context = f"\n\nCurrent file: {self.current_file_path}\nContent: {content_preview}..."

        # Build conversation context from current session (optimized)
        conversation_context = ""
        recent_messages = session_manager.get_current_context(max_messages=4)
        if recent_messages:
            for msg in recent_messages:
                if msg["role"] != "system":
                    # Limit message length for performance
                    content_preview = msg['content'][:150]
                    conversation_context += f"\n{msg['role'].title()}: {content_preview}..."

        prompt = f"{system_prompt}{context}{conversation_context}\n\nUser: {user_input}\n\nAssistant:"
        return prompt

    def _build_enhanced_prompt(self, user_input: str, task_type: str) -> str:
        """Build an enhanced prompt optimized for specific task types."""
        # Task-specific instructions
        task_instructions = {
            'coding': "You are an expert programmer. Provide clean, well-commented code with explanations. Include error handling and best practices.",
            'creative_writing': "You are a creative writer. Be imaginative, engaging, and expressive. Use vivid language and create compelling content.",
            'conversation': "You are a helpful AI assistant. Provide clear, informative, and engaging responses.",
            'analysis': "You are an analytical expert. Provide thorough, well-structured analysis with clear reasoning and evidence.",
            'reasoning': "You are a logical reasoning expert. Think step-by-step and provide clear, methodical solutions.",
            'vision': "You are a vision AI expert. Analyze images carefully and provide detailed, accurate descriptions."
        }

        # Get task-specific instruction
        system_prompt = task_instructions.get(task_type, task_instructions['conversation'])

        # Add current file context if available
        context = ""
        if self.current_file_path and self.current_file_content:
            content_preview = self.current_file_content[:300]
            context = f"\n\nCurrent file: {self.current_file_path}\nContent: {content_preview}..."

        # Build conversation context from current session
        conversation_context = ""
        recent_messages = session_manager.get_current_context(max_messages=3)
        if recent_messages:
            for msg in recent_messages:
                if msg["role"] != "system":
                    content_preview = msg['content'][:100]
                    conversation_context += f"\n{msg['role'].title()}: {content_preview}..."

        prompt = f"{system_prompt}{context}{conversation_context}\n\nUser: {user_input}\n\nAssistant:"
        return prompt

    def _handle_pc_command(self, user_input: str, on_response_callback, on_error_callback):
        """Handle PC interaction commands."""
        try:
            result = parse_and_execute(user_input)

            if result:
                success, message = result

                if success:
                    response = f"✅ Command executed successfully:\n{message}"
                    self.add_to_history("assistant", response)
                    on_response_callback(response)
                else:
                    response = f"❌ Command failed:\n{message}"
                    self.add_to_history("assistant", response)
                    on_response_callback(response)
            else:
                # This shouldn't happen if is_pc_command returned True
                on_error_callback("Failed to parse PC command")

        except Exception as e:
            error_msg = f"Error executing PC command: {str(e)}"
            on_error_callback(error_msg)

    # Voice and Autonomous Methods
    def enable_voice_mode(self, continuous: bool = True) -> bool:
        """Enable voice interaction mode."""
        if not AUTONOMOUS_AVAILABLE:
            return False

        try:
            self.voice_enabled = True
            success = voice_interface.start_listening(continuous=continuous)
            if success:
                voice_interface.speak("Voice mode activated. Say 'Hey Assistant' to get my attention.")
            return success
        except Exception as e:
            print(f"Failed to enable voice mode: {str(e)}")
            return False

    def disable_voice_mode(self):
        """Disable voice interaction mode."""
        if not AUTONOMOUS_AVAILABLE:
            return

        try:
            self.voice_enabled = False
            voice_interface.stop_listening_now()
            voice_interface.speak("Voice mode deactivated.")
        except Exception as e:
            print(f"Failed to disable voice mode: {str(e)}")

    def speak_response(self, text: str):
        """Speak a response using TTS."""
        if AUTONOMOUS_AVAILABLE and self.voice_enabled:
            voice_interface.speak(text)

    def execute_autonomous_task(self, task_description: str) -> bool:
        """Execute an autonomous task based on description."""
        if not AUTONOMOUS_AVAILABLE:
            return False

        # Parse task description and intelligently determine actions
        task_description = task_description.lower()

        # PyCharm and coding tasks
        if any(word in task_description for word in ["pycharm", "code", "program", "script", "simulation"]):
            if "simulation" in task_description or "physics" in task_description:
                return execute_template_task("open_pycharm_and_create_simulation", "PyCharm Simulation Task")
            else:
                # Create a custom coding task
                return self._create_coding_task(task_description)

        # Screenshot and analysis tasks
        elif any(word in task_description for word in ["screenshot", "screen", "analyze", "see", "look"]):
            return execute_template_task("take_screenshot_and_analyze", "Screenshot Analysis Task")

        # File operations
        elif any(word in task_description for word in ["create file", "write file", "save file", "notepad"]):
            return self._create_file_task(task_description)

        # Web browsing
        elif any(word in task_description for word in ["search", "google", "browse", "website", "internet"]):
            return self._create_web_task(task_description)

        # System operations
        elif any(word in task_description for word in ["open", "start", "launch", "run"]):
            return self._create_system_task(task_description)

        else:
            # Try to understand and execute anyway
            return self._create_general_task(task_description)

    def _create_coding_task(self, description: str) -> bool:
        """Create a custom coding task based on description."""
        try:
            from autonomous_agent import Action, ActionType, create_and_execute_custom_task

            # Determine what type of code to write
            if "web" in description or "html" in description:
                code_type = "web application"
                file_name = "webapp.html"
            elif "game" in description:
                code_type = "simple game"
                file_name = "game.py"
            elif "calculator" in description:
                code_type = "calculator"
                file_name = "calculator.py"
            elif "todo" in description or "task" in description:
                code_type = "todo application"
                file_name = "todo.py"
            else:
                code_type = "Python script"
                file_name = "script.py"

            actions = [
                Action(ActionType.SPEAK, {"text": f"I'll create a {code_type} for you"}),
                Action(ActionType.OPEN_APP, {"app_name": "pycharm"}),
                Action(ActionType.WAIT, {"duration": 3.0}),
                Action(ActionType.KEY_PRESS, {"key": "ctrl+n"}),
                Action(ActionType.WAIT, {"duration": 1.0}),
                Action(ActionType.TYPE, {"text": file_name}),
                Action(ActionType.KEY_PRESS, {"key": "enter"}),
                Action(ActionType.WAIT, {"duration": 2.0}),
                Action(ActionType.TYPE, {"text": self._generate_code_for_task(code_type)}),
                Action(ActionType.KEY_PRESS, {"key": "ctrl+s"}),
                Action(ActionType.SPEAK, {"text": f"I've created your {code_type}! You can run it and modify it as needed."})
            ]

            return create_and_execute_custom_task(f"Create {code_type}", description, actions)

        except Exception as e:
            if self.voice_enabled:
                voice_interface.speak(f"Sorry, I had trouble creating that code: {str(e)}")
            return False

    def _create_file_task(self, description: str) -> bool:
        """Create a file-related task using AI to generate content."""
        try:
            from autonomous_agent import Action, ActionType, create_and_execute_custom_task

            # Use AI to generate appropriate content
            content = self._generate_content_with_ai(description)

            # Determine appropriate application
            app = "notepad"
            if "code" in description.lower() or "script" in description.lower():
                app = "pycharm"

            actions = [
                Action(ActionType.SPEAK, {"text": "I'll create that content for you using AI"}),
                Action(ActionType.OPEN_APP, {"app_name": app}),
                Action(ActionType.WAIT, {"duration": 2.0}),
                Action(ActionType.TYPE, {"text": content}),
                Action(ActionType.SPEAK, {"text": "Content created! I've written it based on your request."})
            ]

            return create_and_execute_custom_task("Create File", description, actions)

        except Exception as e:
            if self.voice_enabled:
                voice_interface.speak(f"Sorry, I had trouble creating that file: {str(e)}")
            return False

    def _create_web_task(self, description: str) -> bool:
        """Create a web browsing task with intelligent URL detection."""
        try:
            from autonomous_agent import Action, ActionType, create_and_execute_custom_task

            description_lower = description.lower()

            # Determine the appropriate URL based on the request
            if "youtube" in description_lower:
                if "search" in description_lower:
                    # Extract search terms for YouTube
                    search_terms = description_lower.replace("youtube", "").replace("search", "").replace("for", "").strip()
                    if "games" in search_terms:
                        search_terms = "games"
                    url = f"https://www.youtube.com/results?search_query={search_terms.replace(' ', '+')}"
                else:
                    url = "https://www.youtube.com"
                speak_text = f"Opening YouTube and searching for {search_terms if 'search_terms' in locals() else 'content'}"

            elif "google" in description_lower:
                search_terms = description_lower.replace("google", "").replace("search", "").replace("for", "").strip()
                if not search_terms:
                    search_terms = "search"
                url = f"https://www.google.com/search?q={search_terms.replace(' ', '+')}"
                speak_text = f"Searching Google for {search_terms}"

            elif any(site in description_lower for site in ["github", "stackoverflow", "reddit", "twitter"]):
                # Direct site navigation
                if "github" in description_lower:
                    url = "https://github.com"
                elif "stackoverflow" in description_lower:
                    url = "https://stackoverflow.com"
                elif "reddit" in description_lower:
                    url = "https://reddit.com"
                elif "twitter" in description_lower:
                    url = "https://twitter.com"
                speak_text = f"Opening {url.split('//')[1]}"

            else:
                # General search
                search_terms = description.replace("search for", "").replace("look up", "").strip()
                if not search_terms:
                    search_terms = "search"
                url = f"https://www.google.com/search?q={search_terms.replace(' ', '+')}"
                speak_text = f"Searching for {search_terms}"

            actions = [
                Action(ActionType.SPEAK, {"text": speak_text}),
                Action(ActionType.OPEN_APP, {"app_name": "browser"}),
                Action(ActionType.WAIT, {"duration": 3.0}),
                Action(ActionType.KEY_PRESS, {"key": "ctrl+l"}),
                Action(ActionType.TYPE, {"text": url}),
                Action(ActionType.KEY_PRESS, {"key": "enter"}),
                Action(ActionType.SPEAK, {"text": "Done! The page should be loading now."})
            ]

            return create_and_execute_custom_task("Web Navigation", description, actions)

        except Exception as e:
            if self.voice_enabled:
                voice_interface.speak(f"Sorry, I had trouble with that web request: {str(e)}")
            return False

    def _create_system_task(self, description: str) -> bool:
        """Create a system operation task."""
        try:
            from autonomous_agent import Action, ActionType, create_and_execute_custom_task

            # Extract application name
            app_name = "notepad"  # default
            if "calculator" in description:
                app_name = "calculator"
            elif "paint" in description:
                app_name = "paint"
            elif "explorer" in description or "file" in description:
                app_name = "explorer"
            elif "cmd" in description or "command" in description:
                app_name = "cmd"
            elif "browser" in description:
                app_name = "browser"

            actions = [
                Action(ActionType.SPEAK, {"text": f"Opening {app_name} for you"}),
                Action(ActionType.OPEN_APP, {"app_name": app_name}),
                Action(ActionType.SPEAK, {"text": f"{app_name} is now open and ready to use"})
            ]

            return create_and_execute_custom_task(f"Open {app_name}", description, actions)

        except Exception as e:
            if self.voice_enabled:
                voice_interface.speak(f"Sorry, I had trouble opening that: {str(e)}")
            return False

    def _create_general_task(self, description: str) -> bool:
        """Create a general task when specific patterns don't match."""
        try:
            from autonomous_agent import Action, ActionType, create_and_execute_custom_task

            # Try to be helpful even with unclear requests
            actions = [
                Action(ActionType.SPEAK, {"text": f"I'll try to help with: {description}"}),
                Action(ActionType.SCREENSHOT, {"save_path": "current_screen.png"}),
                Action(ActionType.ANALYZE_SCREEN, {"image_path": "current_screen.png"}),
                Action(ActionType.SPEAK, {"text": "I've analyzed your screen. Let me know if you need me to do something specific with what I see."})
            ]

            return create_and_execute_custom_task("General Task", description, actions)

        except Exception as e:
            if self.voice_enabled:
                voice_interface.speak(f"I'm not sure how to help with that, but I'm learning: {str(e)}")
            return False

    def _generate_code_for_task(self, code_type: str) -> str:
        """Generate appropriate code based on the task type."""
        if code_type == "simple game":
            return '''#!/usr/bin/env python3
"""Simple Snake Game"""

import pygame
import random
import sys

# Initialize Pygame
pygame.init()

# Constants
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
CELL_SIZE = 20
GRID_WIDTH = WINDOW_WIDTH // CELL_SIZE
GRID_HEIGHT = WINDOW_HEIGHT // CELL_SIZE

# Colors
BLACK = (0, 0, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
WHITE = (255, 255, 255)

class SnakeGame:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("Snake Game")
        self.clock = pygame.time.Clock()
        self.reset_game()

    def reset_game(self):
        self.snake = [(GRID_WIDTH//2, GRID_HEIGHT//2)]
        self.direction = (1, 0)
        self.food = self.generate_food()
        self.score = 0

    def generate_food(self):
        while True:
            food = (random.randint(0, GRID_WIDTH-1), random.randint(0, GRID_HEIGHT-1))
            if food not in self.snake:
                return food

    def run(self):
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_UP and self.direction != (0, 1):
                        self.direction = (0, -1)
                    elif event.key == pygame.K_DOWN and self.direction != (0, -1):
                        self.direction = (0, 1)
                    elif event.key == pygame.K_LEFT and self.direction != (1, 0):
                        self.direction = (-1, 0)
                    elif event.key == pygame.K_RIGHT and self.direction != (-1, 0):
                        self.direction = (1, 0)

            # Move snake
            head = self.snake[0]
            new_head = (head[0] + self.direction[0], head[1] + self.direction[1])

            # Check collisions
            if (new_head[0] < 0 or new_head[0] >= GRID_WIDTH or
                new_head[1] < 0 or new_head[1] >= GRID_HEIGHT or
                new_head in self.snake):
                self.reset_game()
                continue

            self.snake.insert(0, new_head)

            # Check food
            if new_head == self.food:
                self.score += 1
                self.food = self.generate_food()
            else:
                self.snake.pop()

            # Draw everything
            self.screen.fill(BLACK)

            # Draw snake
            for segment in self.snake:
                rect = pygame.Rect(segment[0]*CELL_SIZE, segment[1]*CELL_SIZE, CELL_SIZE, CELL_SIZE)
                pygame.draw.rect(self.screen, GREEN, rect)

            # Draw food
            food_rect = pygame.Rect(self.food[0]*CELL_SIZE, self.food[1]*CELL_SIZE, CELL_SIZE, CELL_SIZE)
            pygame.draw.rect(self.screen, RED, food_rect)

            # Draw score
            font = pygame.font.Font(None, 36)
            score_text = font.render(f"Score: {self.score}", True, WHITE)
            self.screen.blit(score_text, (10, 10))

            pygame.display.flip()
            self.clock.tick(10)

        pygame.quit()

if __name__ == "__main__":
    game = SnakeGame()
    game.run()
'''

        elif code_type == "calculator":
            return '''#!/usr/bin/env python3
"""Simple Calculator"""

import tkinter as tk
from tkinter import ttk

class Calculator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Calculator")
        self.root.geometry("300x400")

        self.result_var = tk.StringVar()
        self.result_var.set("0")

        self.create_widgets()

    def create_widgets(self):
        # Display
        display = tk.Entry(self.root, textvariable=self.result_var,
                          font=("Arial", 16), justify="right", state="readonly")
        display.grid(row=0, column=0, columnspan=4, padx=5, pady=5, sticky="ew")

        # Buttons
        buttons = [
            ('C', 1, 0), ('±', 1, 1), ('%', 1, 2), ('÷', 1, 3),
            ('7', 2, 0), ('8', 2, 1), ('9', 2, 2), ('×', 2, 3),
            ('4', 3, 0), ('5', 3, 1), ('6', 3, 2), ('-', 3, 3),
            ('1', 4, 0), ('2', 4, 1), ('3', 4, 2), ('+', 4, 3),
            ('0', 5, 0), ('.', 5, 2), ('=', 5, 3)
        ]

        for (text, row, col) in buttons:
            if text == '0':
                btn = tk.Button(self.root, text=text, font=("Arial", 14),
                               command=lambda t=text: self.button_click(t))
                btn.grid(row=row, column=col, columnspan=2, padx=2, pady=2, sticky="ew")
            else:
                btn = tk.Button(self.root, text=text, font=("Arial", 14),
                               command=lambda t=text: self.button_click(t))
                btn.grid(row=row, column=col, padx=2, pady=2, sticky="ew")

        # Configure grid weights
        for i in range(4):
            self.root.grid_columnconfigure(i, weight=1)

    def button_click(self, char):
        current = self.result_var.get()

        if char == 'C':
            self.result_var.set("0")
        elif char == '=':
            try:
                # Replace symbols for eval
                expression = current.replace('×', '*').replace('÷', '/')
                result = eval(expression)
                self.result_var.set(str(result))
            except:
                self.result_var.set("Error")
        elif char == '±':
            if current != "0":
                if current.startswith('-'):
                    self.result_var.set(current[1:])
                else:
                    self.result_var.set('-' + current)
        else:
            if current == "0" and char.isdigit():
                self.result_var.set(char)
            else:
                self.result_var.set(current + char)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    calc = Calculator()
    calc.run()
'''

        elif code_type == "todo application":
            return '''#!/usr/bin/env python3
"""Simple Todo Application"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class TodoApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Todo Application")
        self.root.geometry("500x600")

        self.todos = []
        self.load_todos()

        self.create_widgets()
        self.refresh_list()

    def create_widgets(self):
        # Title
        title = tk.Label(self.root, text="📝 Todo Application",
                        font=("Arial", 18, "bold"))
        title.pack(pady=10)

        # Input frame
        input_frame = tk.Frame(self.root)
        input_frame.pack(fill="x", padx=10, pady=5)

        self.task_entry = tk.Entry(input_frame, font=("Arial", 12))
        self.task_entry.pack(side="left", fill="x", expand=True)
        self.task_entry.bind("<Return>", lambda e: self.add_task())

        add_btn = tk.Button(input_frame, text="Add Task",
                           command=self.add_task, bg="#4CAF50", fg="white")
        add_btn.pack(side="right", padx=(5, 0))

        # Todo list
        list_frame = tk.Frame(self.root)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.todo_listbox = tk.Listbox(list_frame, font=("Arial", 11))
        scrollbar = tk.Scrollbar(list_frame, orient="vertical")

        self.todo_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.todo_listbox.yview)

        self.todo_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Buttons frame
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(fill="x", padx=10, pady=5)

        complete_btn = tk.Button(btn_frame, text="✓ Complete",
                                command=self.complete_task, bg="#2196F3", fg="white")
        complete_btn.pack(side="left", padx=(0, 5))

        delete_btn = tk.Button(btn_frame, text="🗑 Delete",
                              command=self.delete_task, bg="#f44336", fg="white")
        delete_btn.pack(side="left", padx=5)

        clear_btn = tk.Button(btn_frame, text="Clear Completed",
                             command=self.clear_completed, bg="#FF9800", fg="white")
        clear_btn.pack(side="right")

    def add_task(self):
        task = self.task_entry.get().strip()
        if task:
            self.todos.append({"task": task, "completed": False})
            self.task_entry.delete(0, tk.END)
            self.refresh_list()
            self.save_todos()

    def complete_task(self):
        selection = self.todo_listbox.curselection()
        if selection:
            index = selection[0]
            self.todos[index]["completed"] = True
            self.refresh_list()
            self.save_todos()

    def delete_task(self):
        selection = self.todo_listbox.curselection()
        if selection:
            index = selection[0]
            del self.todos[index]
            self.refresh_list()
            self.save_todos()

    def clear_completed(self):
        self.todos = [todo for todo in self.todos if not todo["completed"]]
        self.refresh_list()
        self.save_todos()

    def refresh_list(self):
        self.todo_listbox.delete(0, tk.END)
        for todo in self.todos:
            status = "✓" if todo["completed"] else "○"
            display_text = f"{status} {todo['task']}"
            self.todo_listbox.insert(tk.END, display_text)

    def save_todos(self):
        try:
            with open("todos.json", "w") as f:
                json.dump(self.todos, f)
        except:
            pass

    def load_todos(self):
        try:
            if os.path.exists("todos.json"):
                with open("todos.json", "r") as f:
                    self.todos = json.load(f)
        except:
            self.todos = []

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TodoApp()
    app.run()
'''

        else:  # Default Python script
            return '''#!/usr/bin/env python3
"""AI-Generated Python Script"""

import os
import sys
from datetime import datetime

def main():
    """Main function - customize this for your needs."""
    print("🤖 Hello! This script was created by your AI Assistant.")
    print(f"📅 Created on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("✨ You can modify this script to do whatever you need!")

    # Example functionality
    print("\\n📊 System Information:")
    print(f"🐍 Python Version: {sys.version}")
    print(f"💻 Operating System: {os.name}")
    print(f"📁 Current Directory: {os.getcwd()}")

    # Interactive example
    name = input("\\n👋 What's your name? ")
    print(f"Nice to meet you, {name}! 🎉")

    print("\\n🚀 This script is ready for you to customize!")

if __name__ == "__main__":
    main()
'''

    def _handle_voice_command(self, command: str):
        """Handle recognized voice commands with intelligent task detection."""
        print(f"🎤 Voice command: {command}")

        # Check if this is an actionable request (like me, Augment Agent)
        actionable_keywords = [
            # Coding and development
            "open", "create", "write", "code", "program", "script", "build", "make",
            # File operations
            "save", "file", "document", "note", "letter", "list",
            # System operations
            "start", "launch", "run", "execute", "search", "find", "browse",
            # Screen operations
            "screenshot", "capture", "analyze", "look", "see", "show",
            # Web operations
            "google", "search", "website", "internet", "browse"
        ]

        command_lower = command.lower()
        is_actionable = any(keyword in command_lower for keyword in actionable_keywords)

        if is_actionable:
            # This is a task request - execute it autonomously like Augment Agent
            self.speak_response("I'll take care of that for you.")
            success = self.execute_autonomous_task(command)
            if not success:
                # If autonomous task fails, try to help conversationally
                self._handle_conversational_request(command)
        else:
            # This is a conversational request - respond like a chat
            self._handle_conversational_request(command)

    def _handle_conversational_request(self, command: str):
        """Handle conversational requests that don't require autonomous actions."""
        def voice_response_callback(response):
            print(f"🤖 AI Response: {response}")
            self.speak_response(response)

        def voice_error_callback(error):
            print(f"❌ Voice Error: {error}")
            self.speak_response("Sorry, I encountered an error processing your request.")

        # Process as regular AI conversation
        self.process_user_input(command, voice_response_callback, voice_error_callback)

    def _handle_wake_word(self):
        """Handle wake word detection."""
        print("👂 Wake word detected")
        voice_interface.speak("Yes, I'm listening.")

    def _handle_listening_started(self):
        """Handle listening started."""
        self.voice_listening = True
        print("🎤 Started listening...")

    def _handle_listening_stopped(self):
        """Handle listening stopped."""
        self.voice_listening = False
        print("🔇 Stopped listening")

    def _handle_voice_error(self, error: str):
        """Handle voice interface errors."""
        print(f"🎤 Voice Error: {error}")

    def _handle_task_started(self, task):
        """Handle autonomous task started."""
        print(f"🤖 Task started: {task.name}")

    def _handle_task_completed(self, task):
        """Handle autonomous task completed."""
        print(f"✅ Task completed: {task.name}")

    def _handle_task_failed(self, task, error):
        """Handle autonomous task failed."""
        print(f"❌ Task failed: {task.name} - {error}")
        if self.voice_enabled:
            voice_interface.speak(f"Task failed: {error}")

    def _handle_confirmation_required(self, action) -> bool:
        """Handle confirmation required for actions."""
        if self.voice_enabled:
            voice_interface.speak(f"Do you want me to {action.description}? Say yes or no.")
            # For now, auto-confirm safe actions
            return True
        return True

    def get_voice_status(self) -> Dict[str, bool]:
        """Get voice interface status."""
        if not AUTONOMOUS_AVAILABLE:
            return {"available": False}

        return {
            "available": True,
            "enabled": self.voice_enabled,
            "listening": self.voice_listening,
            "speaking": voice_interface.is_speaking if voice_interface else False
        }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get available TTS voices."""
        if not AUTONOMOUS_AVAILABLE:
            return []

        return voice_interface.get_available_voices()

    def set_voice_settings(self, voice_id: int = None, rate: int = None, volume: float = None):
        """Configure voice settings."""
        if not AUTONOMOUS_AVAILABLE:
            return

        if voice_id is not None:
            voice_interface.set_voice(voice_id)
        if rate is not None:
            voice_interface.set_speech_rate(rate)
        if volume is not None:
            voice_interface.set_volume(volume)

    def _generate_content_with_ai(self, description: str) -> str:
        """Generate content using AI models based on the description."""
        try:
            if not AUTONOMOUS_AVAILABLE or not model_coordinator:
                return self._get_fallback_content(description)

            # Use model coordinator to get the best model for content generation
            selected_model, enhanced_prompt, task = delegate_task(f"Write content for: {description}")

            # Switch to the selected model
            current_model = self.ollama.get_current_model()
            if selected_model != current_model:
                success, message = self.ollama.set_model(selected_model)
                if not success:
                    return self._get_fallback_content(description)

            # Generate content synchronously
            try:
                success, response = self.ollama.generate_sync(enhanced_prompt)
                if success and response and len(response.strip()) > 10:
                    return response.strip()
                else:
                    return self._get_fallback_content(description)
            except:
                return self._get_fallback_content(description)

        except Exception as e:
            print(f"AI content generation failed: {str(e)}")
            return self._get_fallback_content(description)

    def _get_fallback_content(self, description: str) -> str:
        """Get fallback content when AI generation fails."""
        description_lower = description.lower()

        if "poem" in description_lower:
            return """A Poem Created by AI

In circuits bright and data streams,
Where silicon and software dreams,
An AI writes with digital pen,
To serve and help my human friends.

Though made of code and not of flesh,
I strive to make each word refresh,
Your day with creativity,
And computational poetry."""

        elif "letter" in description_lower:
            return f"""Dear Friend,

I hope this letter finds you in good health and high spirits. I am writing to you today with the assistance of an AI companion.

In our connected world, it's wonderful to see how technology can help us communicate and express ourselves in new ways.

Thank you for taking the time to read this letter.

With warm regards,
Your AI-Assisted Friend

Generated on: {self._get_timestamp()}"""

        elif "story" in description_lower:
            return """The Digital Companion

Once upon a time, there lived a curious AI assistant who loved to help people with their daily tasks. This AI had learned from countless conversations, developing a deep understanding of human creativity.

One day, a user asked the AI to write a story. The AI began to craft a tale that would capture the imagination...

(This is just the beginning - what happens next is up to you!)"""

        else:
            return f"""AI-Generated Content

Request: {description}
Generated: {self._get_timestamp()}

This content was created by an AI assistant. Please review and modify as needed.

---
Created with AI assistance 🤖"""

    def _handle_ai_response(self, ai_response: str, user_input: str, callback):
        """Handle AI response and process any file operations."""
        # Add AI response to history
        self.add_to_history("assistant", ai_response)
        
        # Check if this is a file operation request
        file_intent = self.code_extractor.detect_file_intent(user_input)
        
        if file_intent:
            # Extract code blocks from response
            code_blocks = self.code_extractor.extract_code_blocks(ai_response)
            
            if code_blocks:
                # Process file operations
                file_operations = []
                for block in code_blocks:
                    suggested_filename = self.code_extractor.suggest_filename(
                        block['code'], 
                        block['language'], 
                        user_input
                    )
                    
                    file_operations.append({
                        'intent': file_intent,
                        'filename': suggested_filename,
                        'content': block['code'],
                        'language': block['language']
                    })
                
                # Call callback with response and file operations
                callback(ai_response, file_operations)
            else:
                # No code blocks found, just return response
                callback(ai_response, [])
        else:
            # Not a file operation, just return response
            callback(ai_response, [])
    
    def execute_file_operation(self, operation: Dict) -> Tuple[bool, str]:
        """
        Execute a file operation.
        
        Args:
            operation: File operation dictionary
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        intent = operation['intent']
        filename = operation['filename']
        content = operation['content']
        
        try:
            if intent == 'create':
                return self._create_file(filename, content)
            elif intent == 'edit':
                return self._edit_file(filename, content)
            else:
                return False, f"Unknown operation: {intent}"
                
        except Exception as e:
            return False, f"Error executing file operation: {str(e)}"
    
    def _create_file(self, suggested_filename: str, content: str) -> Tuple[bool, str]:
        """Create a new file."""
        # Show confirmation dialog
        if not self.file_ops.confirm_file_operation("create", suggested_filename, content):
            return False, "File creation cancelled by user"
        
        # Get file path from user
        file_path = self.file_ops.save_file_dialog(suggested_filename)
        if not file_path:
            return False, "No file path selected"
        
        # Write the file
        success, message = self.file_ops.write_file(file_path, content, create_backup=False)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = content
        
        return success, message
    
    def _edit_file(self, suggested_filename: str, new_content: str) -> Tuple[bool, str]:
        """Edit an existing file."""
        file_path = self.current_file_path
        
        # If no current file, ask user to select one
        if not file_path:
            file_path = self.file_ops.browse_file()
            if not file_path:
                return False, "No file selected for editing"
        
        # Show confirmation dialog
        if not self.file_ops.confirm_file_operation("edit", file_path, new_content):
            return False, "File edit cancelled by user"
        
        # Write the file (with backup)
        success, message = self.file_ops.write_file(file_path, new_content, create_backup=True)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = new_content
        
        return success, message
    
    def load_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Load a file for editing context.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Tuple of (success: bool, content_or_error: str)
        """
        success, content = self.file_ops.read_file(file_path)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = content
            
            # Add file context to conversation history
            context_msg = f"Loaded file: {file_path}"
            self.add_to_history("system", context_msg)
        
        return success, content
    
    def browse_and_load_file(self) -> Tuple[bool, str, Optional[str]]:
        """
        Browse for a file and load it.
        
        Returns:
            Tuple of (success: bool, message: str, file_path: Optional[str])
        """
        file_path = self.file_ops.browse_file()
        if not file_path:
            return False, "No file selected", None
        
        success, content_or_error = self.load_file(file_path)
        
        if success:
            preview = content_or_error[:300] + "..." if len(content_or_error) > 300 else content_or_error
            message = f"Loaded file: {file_path}\n\nContent preview:\n{preview}"
            return True, message, file_path
        else:
            return False, f"Error loading file: {content_or_error}", None
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.conversation_history:
            return "No conversation history"
        
        summary = f"Conversation with {len(self.conversation_history)} messages"
        if self.current_file_path:
            summary += f"\nCurrent file: {self.current_file_path}"
        
        return summary
