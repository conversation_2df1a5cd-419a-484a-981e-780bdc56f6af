"""Core AI assistant logic for processing requests and managing file operations."""

import re
import gc
from typing import List, Dict, Optional, <PERSON><PERSON>
from enhanced_config import MAX_CONVERSATION_HISTORY
from enhanced_ollama_api import Enhanced<PERSON>llamaAP<PERSON>
from file_operations import FileOperations, CodeExtractor
from command_parser import parse_and_execute, is_pc_command, get_command_help
from chat_session_manager import session_manager

# Import new autonomous capabilities
try:
    from voice_interface import voice_interface, VoiceSettings
    from screen_automation import screen_automation, AutomationSettings
    from autonomous_agent import autonomous_agent, execute_template_task
    AUTONOMOUS_AVAILABLE = True
except ImportError:
    AUTONOMOUS_AVAILABLE = False
    voice_interface = None
    screen_automation = None
    autonomous_agent = None


class AIAssistant:
    """Main AI assistant that coordinates between GUI, API, and file operations."""
    
    def __init__(self):
        self.ollama = EnhancedOllamaAPI()
        self.file_ops = FileOperations()
        self.code_extractor = CodeExtractor()

        # Current context
        self.current_file_path = None
        self.current_file_content = None

        # Voice and autonomous capabilities
        self.voice_enabled = False
        self.autonomous_mode = False
        self.voice_listening = False

        # Initialize with default session if none exists
        if not session_manager.get_current_session():
            session_manager.create_new_session("phi3:mini", "Default Chat")

        # Initialize autonomous capabilities if available
        self._initialize_autonomous_features()

    def _initialize_autonomous_features(self):
        """Initialize voice and autonomous capabilities."""
        if not AUTONOMOUS_AVAILABLE:
            return

        try:
            # Set up voice interface callbacks
            voice_interface.on_speech_recognized = self._handle_voice_command
            voice_interface.on_wake_word_detected = self._handle_wake_word
            voice_interface.on_listening_started = self._handle_listening_started
            voice_interface.on_listening_stopped = self._handle_listening_stopped
            voice_interface.on_error = self._handle_voice_error

            # Set up autonomous agent callbacks
            autonomous_agent.on_task_started = self._handle_task_started
            autonomous_agent.on_task_completed = self._handle_task_completed
            autonomous_agent.on_task_failed = self._handle_task_failed
            autonomous_agent.on_confirmation_required = self._handle_confirmation_required

            print("✅ Autonomous features initialized successfully")

        except Exception as e:
            print(f"⚠️ Failed to initialize autonomous features: {str(e)}")

    def test_connection(self) -> Tuple[bool, str]:
        """Test connection to Ollama."""
        return self.ollama.test_connection()
    
    def add_to_history(self, role: str, content: str):
        """Add message to current session's conversation history."""
        session_manager.add_message_to_current(role, content)

    def clear_conversation(self):
        """Clear current session's conversation history."""
        session_manager.clear_current_session()

    def switch_model(self, model_name: str) -> str:
        """Switch to a different model, creating or resuming session."""
        session_id = session_manager.switch_model(model_name, create_new=True)
        return session_id

    def create_new_chat(self, model_name: str = None, session_name: str = None) -> str:
        """Create a new chat session."""
        if not model_name:
            current_session = session_manager.get_current_session()
            model_name = current_session.model_name if current_session else "phi3:mini"

        session_id = session_manager.create_new_session(model_name, session_name)
        return session_id

    def get_current_session_info(self) -> dict:
        """Get information about current session."""
        session = session_manager.get_current_session()
        if session:
            return {
                "session_id": session.session_id,
                "model_name": session.model_name,
                "session_name": session.session_name,
                "message_count": session.message_count,
                "created_at": session.created_at.strftime("%Y-%m-%d %H:%M"),
                "last_updated": session.last_updated.strftime("%Y-%m-%d %H:%M")
            }
        return None

    def get_all_sessions(self) -> list:
        """Get all available sessions."""
        sessions = session_manager.get_all_sessions()
        return [
            {
                "session_id": s.session_id,
                "model_name": s.model_name,
                "session_name": s.session_name,
                "message_count": s.message_count,
                "summary": s.get_summary()
            }
            for s in sessions
        ]
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def process_user_input(self, user_input: str,
                          on_response_callback,
                          on_error_callback,
                          use_streaming: bool = False):
        """
        Process user input and generate AI response with PC interaction support.

        Args:
            user_input: User's message
            on_response_callback: Callback for AI response
            on_error_callback: Callback for errors
            use_streaming: Whether to use streaming for faster perceived response
        """
        # Add user input to history
        self.add_to_history("user", user_input)

        # Check if this is a PC interaction command
        if is_pc_command(user_input):
            self._handle_pc_command(user_input, on_response_callback, on_error_callback)
            return

        # Check for help request
        if any(word in user_input.lower() for word in ['help', 'commands', 'what can you do']):
            if 'pc' in user_input.lower() or 'computer' in user_input.lower() or 'system' in user_input.lower():
                help_text = get_command_help()
                self._handle_ai_response(help_text, user_input, on_response_callback)
                return

        # Build optimized prompt with context
        prompt = self._build_prompt(user_input)

        # Optimize prompt length for faster responses
        if len(prompt) > 1500:
            prompt = prompt[:1500] + "..."

        if use_streaming:
            # Use streaming for faster perceived response time
            accumulated_response = ""

            def on_chunk(chunk):
                nonlocal accumulated_response
                accumulated_response += chunk
                # Could update GUI with partial response here

            def on_complete(full_response):
                self._handle_ai_response(full_response, user_input, on_response_callback)

            self.ollama.generate_streaming(
                prompt,
                on_chunk,
                on_complete,
                on_error_callback
            )
        else:
            # Use optimized async generation
            self.ollama.generate_async(
                prompt,
                lambda response: self._handle_ai_response(response, user_input, on_response_callback),
                on_error_callback,
                optimize_for_speed=True
            )
    
    def _build_prompt(self, user_input: str) -> str:
        """Build context-aware prompt for the AI (optimized)."""
        # Use a more concise system prompt for better performance
        system_prompt = """You are a helpful AI assistant with PC interaction capabilities. You can help with:

• Conversations, writing, analysis, problem-solving
• Programming and file creation
• PC interaction: running programs, executing commands, managing files
• System operations: file management, directory listing, program launching

When creating files, use properly formatted code blocks with triple backticks and language specification.

For PC interactions, users can use commands like:
- "open notepad" - Launch programs
- "create file example.txt" - Create files
- "list directory" - Show folder contents
- "run command dir" - Execute system commands

Be helpful, conversational, and adapt to the user's needs."""

        # Add current file context if available (truncated for performance)
        context = ""
        if self.current_file_path and self.current_file_content:
            # Limit context size for better performance
            content_preview = self.current_file_content[:500]
            context = f"\n\nCurrent file: {self.current_file_path}\nContent: {content_preview}..."

        # Build conversation context from current session (optimized)
        conversation_context = ""
        recent_messages = session_manager.get_current_context(max_messages=4)
        if recent_messages:
            for msg in recent_messages:
                if msg["role"] != "system":
                    # Limit message length for performance
                    content_preview = msg['content'][:150]
                    conversation_context += f"\n{msg['role'].title()}: {content_preview}..."

        prompt = f"{system_prompt}{context}{conversation_context}\n\nUser: {user_input}\n\nAssistant:"
        return prompt

    def _handle_pc_command(self, user_input: str, on_response_callback, on_error_callback):
        """Handle PC interaction commands."""
        try:
            result = parse_and_execute(user_input)

            if result:
                success, message = result

                if success:
                    response = f"✅ Command executed successfully:\n{message}"
                    self.add_to_history("assistant", response)
                    on_response_callback(response)
                else:
                    response = f"❌ Command failed:\n{message}"
                    self.add_to_history("assistant", response)
                    on_response_callback(response)
            else:
                # This shouldn't happen if is_pc_command returned True
                on_error_callback("Failed to parse PC command")

        except Exception as e:
            error_msg = f"Error executing PC command: {str(e)}"
            on_error_callback(error_msg)

    # Voice and Autonomous Methods
    def enable_voice_mode(self, continuous: bool = True) -> bool:
        """Enable voice interaction mode."""
        if not AUTONOMOUS_AVAILABLE:
            return False

        try:
            self.voice_enabled = True
            success = voice_interface.start_listening(continuous=continuous)
            if success:
                voice_interface.speak("Voice mode activated. Say 'Hey Assistant' to get my attention.")
            return success
        except Exception as e:
            print(f"Failed to enable voice mode: {str(e)}")
            return False

    def disable_voice_mode(self):
        """Disable voice interaction mode."""
        if not AUTONOMOUS_AVAILABLE:
            return

        try:
            self.voice_enabled = False
            voice_interface.stop_listening_now()
            voice_interface.speak("Voice mode deactivated.")
        except Exception as e:
            print(f"Failed to disable voice mode: {str(e)}")

    def speak_response(self, text: str):
        """Speak a response using TTS."""
        if AUTONOMOUS_AVAILABLE and self.voice_enabled:
            voice_interface.speak(text)

    def execute_autonomous_task(self, task_description: str) -> bool:
        """Execute an autonomous task based on description."""
        if not AUTONOMOUS_AVAILABLE:
            return False

        # Parse task description and map to templates
        task_description = task_description.lower()

        if "open pycharm" in task_description and ("simulation" in task_description or "code" in task_description):
            return execute_template_task("open_pycharm_and_create_simulation", "PyCharm Simulation Task")
        elif "screenshot" in task_description and "analyze" in task_description:
            return execute_template_task("take_screenshot_and_analyze", "Screenshot Analysis Task")
        else:
            # For now, speak that the task is not recognized
            voice_interface.speak(f"I don't know how to execute this task yet: {task_description}")
            return False

    def _handle_voice_command(self, command: str):
        """Handle recognized voice commands."""
        print(f"🎤 Voice command: {command}")

        # Process the voice command as if it were typed input
        def voice_response_callback(response):
            print(f"🤖 AI Response: {response}")
            self.speak_response(response)

        def voice_error_callback(error):
            print(f"❌ Voice Error: {error}")
            self.speak_response("Sorry, I encountered an error processing your request.")

        # Check if this is an autonomous task request
        if any(phrase in command.lower() for phrase in ["open pycharm", "create simulation", "take screenshot", "analyze screen"]):
            success = self.execute_autonomous_task(command)
            if not success:
                self.speak_response("I couldn't execute that autonomous task.")
        else:
            # Process as regular AI conversation
            self.process_user_input(command, voice_response_callback, voice_error_callback)

    def _handle_wake_word(self):
        """Handle wake word detection."""
        print("👂 Wake word detected")
        voice_interface.speak("Yes, I'm listening.")

    def _handle_listening_started(self):
        """Handle listening started."""
        self.voice_listening = True
        print("🎤 Started listening...")

    def _handle_listening_stopped(self):
        """Handle listening stopped."""
        self.voice_listening = False
        print("🔇 Stopped listening")

    def _handle_voice_error(self, error: str):
        """Handle voice interface errors."""
        print(f"🎤 Voice Error: {error}")

    def _handle_task_started(self, task):
        """Handle autonomous task started."""
        print(f"🤖 Task started: {task.name}")

    def _handle_task_completed(self, task):
        """Handle autonomous task completed."""
        print(f"✅ Task completed: {task.name}")

    def _handle_task_failed(self, task, error):
        """Handle autonomous task failed."""
        print(f"❌ Task failed: {task.name} - {error}")
        if self.voice_enabled:
            voice_interface.speak(f"Task failed: {error}")

    def _handle_confirmation_required(self, action) -> bool:
        """Handle confirmation required for actions."""
        if self.voice_enabled:
            voice_interface.speak(f"Do you want me to {action.description}? Say yes or no.")
            # For now, auto-confirm safe actions
            return True
        return True

    def get_voice_status(self) -> Dict[str, bool]:
        """Get voice interface status."""
        if not AUTONOMOUS_AVAILABLE:
            return {"available": False}

        return {
            "available": True,
            "enabled": self.voice_enabled,
            "listening": self.voice_listening,
            "speaking": voice_interface.is_speaking if voice_interface else False
        }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get available TTS voices."""
        if not AUTONOMOUS_AVAILABLE:
            return []

        return voice_interface.get_available_voices()

    def set_voice_settings(self, voice_id: int = None, rate: int = None, volume: float = None):
        """Configure voice settings."""
        if not AUTONOMOUS_AVAILABLE:
            return

        if voice_id is not None:
            voice_interface.set_voice(voice_id)
        if rate is not None:
            voice_interface.set_speech_rate(rate)
        if volume is not None:
            voice_interface.set_volume(volume)

    def _handle_ai_response(self, ai_response: str, user_input: str, callback):
        """Handle AI response and process any file operations."""
        # Add AI response to history
        self.add_to_history("assistant", ai_response)
        
        # Check if this is a file operation request
        file_intent = self.code_extractor.detect_file_intent(user_input)
        
        if file_intent:
            # Extract code blocks from response
            code_blocks = self.code_extractor.extract_code_blocks(ai_response)
            
            if code_blocks:
                # Process file operations
                file_operations = []
                for block in code_blocks:
                    suggested_filename = self.code_extractor.suggest_filename(
                        block['code'], 
                        block['language'], 
                        user_input
                    )
                    
                    file_operations.append({
                        'intent': file_intent,
                        'filename': suggested_filename,
                        'content': block['code'],
                        'language': block['language']
                    })
                
                # Call callback with response and file operations
                callback(ai_response, file_operations)
            else:
                # No code blocks found, just return response
                callback(ai_response, [])
        else:
            # Not a file operation, just return response
            callback(ai_response, [])
    
    def execute_file_operation(self, operation: Dict) -> Tuple[bool, str]:
        """
        Execute a file operation.
        
        Args:
            operation: File operation dictionary
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        intent = operation['intent']
        filename = operation['filename']
        content = operation['content']
        
        try:
            if intent == 'create':
                return self._create_file(filename, content)
            elif intent == 'edit':
                return self._edit_file(filename, content)
            else:
                return False, f"Unknown operation: {intent}"
                
        except Exception as e:
            return False, f"Error executing file operation: {str(e)}"
    
    def _create_file(self, suggested_filename: str, content: str) -> Tuple[bool, str]:
        """Create a new file."""
        # Show confirmation dialog
        if not self.file_ops.confirm_file_operation("create", suggested_filename, content):
            return False, "File creation cancelled by user"
        
        # Get file path from user
        file_path = self.file_ops.save_file_dialog(suggested_filename)
        if not file_path:
            return False, "No file path selected"
        
        # Write the file
        success, message = self.file_ops.write_file(file_path, content, create_backup=False)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = content
        
        return success, message
    
    def _edit_file(self, suggested_filename: str, new_content: str) -> Tuple[bool, str]:
        """Edit an existing file."""
        file_path = self.current_file_path
        
        # If no current file, ask user to select one
        if not file_path:
            file_path = self.file_ops.browse_file()
            if not file_path:
                return False, "No file selected for editing"
        
        # Show confirmation dialog
        if not self.file_ops.confirm_file_operation("edit", file_path, new_content):
            return False, "File edit cancelled by user"
        
        # Write the file (with backup)
        success, message = self.file_ops.write_file(file_path, new_content, create_backup=True)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = new_content
        
        return success, message
    
    def load_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Load a file for editing context.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Tuple of (success: bool, content_or_error: str)
        """
        success, content = self.file_ops.read_file(file_path)
        
        if success:
            self.current_file_path = file_path
            self.current_file_content = content
            
            # Add file context to conversation history
            context_msg = f"Loaded file: {file_path}"
            self.add_to_history("system", context_msg)
        
        return success, content
    
    def browse_and_load_file(self) -> Tuple[bool, str, Optional[str]]:
        """
        Browse for a file and load it.
        
        Returns:
            Tuple of (success: bool, message: str, file_path: Optional[str])
        """
        file_path = self.file_ops.browse_file()
        if not file_path:
            return False, "No file selected", None
        
        success, content_or_error = self.load_file(file_path)
        
        if success:
            preview = content_or_error[:300] + "..." if len(content_or_error) > 300 else content_or_error
            message = f"Loaded file: {file_path}\n\nContent preview:\n{preview}"
            return True, message, file_path
        else:
            return False, f"Error loading file: {content_or_error}", None
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.conversation_history:
            return "No conversation history"
        
        summary = f"Conversation with {len(self.conversation_history)} messages"
        if self.current_file_path:
            summary += f"\nCurrent file: {self.current_file_path}"
        
        return summary
