#!/usr/bin/env python3
"""Model configuration dialog for setting up delegation rules."""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import time
from typing import Dict, List, Optional, Callable

class ModelConfigDialog:
    """Dialog for configuring model delegation rules."""
    
    def __init__(self, parent, available_models: List[str], on_save_callback: Optional[Callable] = None):
        self.parent = parent
        self.available_models = available_models
        self.on_save_callback = on_save_callback
        self.delegation_rules = {}
        
        self.load_current_config()
        self.create_dialog()
    
    def load_current_config(self):
        """Load current delegation configuration."""
        try:
            if os.path.exists('delegation_config.json'):
                with open('delegation_config.json', 'r') as f:
                    self.delegation_rules = json.load(f)
            else:
                # Default configuration
                self.delegation_rules = {
                    'mother_model': 'Auto-Select',
                    'task_delegation': {
                        'coding': {'primary': '', 'secondary': '', 'fast': ''},
                        'creative_writing': {'primary': '', 'secondary': '', 'fast': ''},
                        'conversation': {'primary': '', 'secondary': '', 'fast': ''},
                        'analysis': {'primary': '', 'secondary': '', 'fast': ''},
                        'reasoning': {'primary': '', 'secondary': '', 'fast': ''},
                        'vision': {'primary': '', 'secondary': '', 'fast': ''}
                    }
                }
        except Exception as e:
            print(f"Error loading config: {str(e)}")
            self.delegation_rules = {}
    
    def create_dialog(self):
        """Create the configuration dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("🧠 Model Delegation Configuration")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"800x600+{x}+{y}")
        
        self.create_widgets()
        self.load_values()
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Header
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        title_label = ttk.Label(
            header_frame,
            text="🧠 Model Delegation Configuration",
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Configure which models to use for different task types",
            font=("Segoe UI", 10)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Mother Model tab
        self.create_mother_model_tab()
        
        # Task Delegation tab
        self.create_task_delegation_tab()
        
        # Performance tab
        self.create_performance_tab()
        
        # Button frame
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.save_button = ttk.Button(
            button_frame,
            text="💾 Save Configuration",
            command=self.save_config
        )
        self.save_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.reset_button = ttk.Button(
            button_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_to_defaults
        )
        self.reset_button.pack(side=tk.LEFT, padx=5)
        
        self.test_button = ttk.Button(
            button_frame,
            text="🧪 Test Configuration",
            command=self.test_config
        )
        self.test_button.pack(side=tk.LEFT, padx=5)
        
        self.close_button = ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.close_dialog
        )
        self.close_button.pack(side=tk.RIGHT)
    
    def create_mother_model_tab(self):
        """Create mother model configuration tab."""
        mother_frame = ttk.Frame(self.notebook)
        self.notebook.add(mother_frame, text="🤖 Mother Model")
        
        # Description
        desc_label = ttk.Label(
            mother_frame,
            text="The Mother Model coordinates task delegation to specialized child models.",
            font=("Segoe UI", 10),
            wraplength=750
        )
        desc_label.pack(pady=10, padx=10)
        
        # Mother model selection
        mother_selection_frame = ttk.LabelFrame(mother_frame, text="Mother Model Selection")
        mother_selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.mother_model_var = tk.StringVar()
        
        # Auto-select option
        auto_radio = ttk.Radiobutton(
            mother_selection_frame,
            text="🎯 Auto-Select (Recommended) - Automatically choose the best model for coordination",
            variable=self.mother_model_var,
            value="Auto-Select"
        )
        auto_radio.pack(anchor=tk.W, padx=10, pady=5)
        
        # Manual selection
        manual_frame = ttk.Frame(mother_selection_frame)
        manual_frame.pack(fill=tk.X, padx=10, pady=5)
        
        manual_radio = ttk.Radiobutton(
            manual_frame,
            text="🔧 Manual Selection:",
            variable=self.mother_model_var,
            value="Manual"
        )
        manual_radio.pack(side=tk.LEFT)
        
        self.manual_mother_combo = ttk.Combobox(
            manual_frame,
            values=self.available_models,
            state='readonly',
            width=30
        )
        self.manual_mother_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # Mother model info
        info_frame = ttk.LabelFrame(mother_frame, text="Mother Model Responsibilities")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = """🧠 The Mother Model handles:
• Analyzing incoming requests to determine task type
• Selecting the most appropriate child model for each task
• Coordinating between different specialized models
• Fallback handling when child models are unavailable
• Performance monitoring and optimization

💡 Recommended Mother Models:
• Auto-Select: Automatically chooses based on performance data
• Large reasoning models (e.g., deepseek-r1:8b, mistral:7b)
• Models with good general capabilities and fast response times"""
        
        info_label = ttk.Label(
            info_frame,
            text=info_text,
            font=("Segoe UI", 9),
            wraplength=750,
            justify=tk.LEFT
        )
        info_label.pack(padx=10, pady=10)
    
    def create_task_delegation_tab(self):
        """Create task delegation configuration tab."""
        task_frame = ttk.Frame(self.notebook)
        self.notebook.add(task_frame, text="🎯 Task Delegation")
        
        # Create scrollable frame
        canvas = tk.Canvas(task_frame)
        scrollbar = ttk.Scrollbar(task_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Task delegation configuration
        self.task_combos = {}
        
        tasks = [
            ("coding", "💻 Coding", "Programming, debugging, code generation"),
            ("creative_writing", "✍️ Creative Writing", "Poems, stories, creative content"),
            ("conversation", "💬 Conversation", "General chat, Q&A, explanations"),
            ("analysis", "📊 Analysis", "Data analysis, comparisons, evaluations"),
            ("reasoning", "🧠 Reasoning", "Logic puzzles, problem solving"),
            ("vision", "👁️ Vision", "Image analysis, visual understanding")
        ]
        
        for i, (task_key, task_name, task_desc) in enumerate(tasks):
            task_frame_widget = ttk.LabelFrame(scrollable_frame, text=task_name)
            task_frame_widget.pack(fill=tk.X, padx=10, pady=5)
            
            # Description
            desc_label = ttk.Label(
                task_frame_widget,
                text=task_desc,
                font=("Segoe UI", 9),
                foreground="gray"
            )
            desc_label.pack(anchor=tk.W, padx=10, pady=(5, 10))
            
            # Model selection grid
            grid_frame = ttk.Frame(task_frame_widget)
            grid_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
            
            self.task_combos[task_key] = {}
            
            priorities = [
                ("primary", "🥇 Primary", "Best quality model for this task"),
                ("secondary", "🥈 Secondary", "Backup model if primary unavailable"),
                ("fast", "⚡ Fast", "Quick response model for urgent requests")
            ]
            
            for j, (priority, priority_name, priority_desc) in enumerate(priorities):
                # Label
                label = ttk.Label(grid_frame, text=priority_name, font=("Segoe UI", 9, "bold"))
                label.grid(row=j, column=0, sticky=tk.W, padx=(0, 10), pady=2)
                
                # Combobox
                combo = ttk.Combobox(
                    grid_frame,
                    values=[""] + self.available_models,
                    state='readonly',
                    width=25
                )
                combo.grid(row=j, column=1, sticky=tk.W, padx=(0, 10), pady=2)
                self.task_combos[task_key][priority] = combo
                
                # Description
                desc = ttk.Label(grid_frame, text=priority_desc, font=("Segoe UI", 8), foreground="gray")
                desc.grid(row=j, column=2, sticky=tk.W, pady=2)
    
    def create_performance_tab(self):
        """Create performance monitoring tab."""
        perf_frame = ttk.Frame(self.notebook)
        self.notebook.add(perf_frame, text="📊 Performance")
        
        # Performance info
        info_label = ttk.Label(
            perf_frame,
            text="📊 Model Performance Data",
            font=("Segoe UI", 14, "bold")
        )
        info_label.pack(pady=10)
        
        # Performance tree
        columns = ("Model", "Best For", "Avg Speed", "Quality", "Accuracy")
        self.perf_tree = ttk.Treeview(perf_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.perf_tree.heading(col, text=col)
            if col == "Model":
                self.perf_tree.column(col, width=200)
            elif col == "Best For":
                self.perf_tree.column(col, width=150)
            else:
                self.perf_tree.column(col, width=100)
        
        # Scrollbar for performance tree
        perf_scrollbar = ttk.Scrollbar(perf_frame, orient=tk.VERTICAL, command=self.perf_tree.yview)
        self.perf_tree.configure(yscrollcommand=perf_scrollbar.set)
        
        self.perf_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        self.load_performance_data()
    
    def load_performance_data(self):
        """Load and display performance data."""
        try:
            # Load benchmark results if available
            if os.path.exists('model_performances.json'):
                with open('model_performances.json', 'r') as f:
                    perf_data = json.load(f)
                
                for model_name, data in perf_data.items():
                    best_tasks = ", ".join(data.get('best_task_types', [])[:2])
                    avg_speed = f"{data.get('avg_response_time', 0):.1f}s"
                    quality = f"{data.get('avg_quality_score', 0):.1f}"
                    accuracy = f"{data.get('avg_accuracy_score', 0):.1f}"
                    
                    self.perf_tree.insert("", tk.END, values=(
                        model_name, best_tasks, avg_speed, quality, accuracy
                    ))
            else:
                # Show placeholder data
                self.perf_tree.insert("", tk.END, values=(
                    "No performance data", "Run benchmarks to see data", "-", "-", "-"
                ))
        except Exception as e:
            print(f"Error loading performance data: {str(e)}")
    
    def load_values(self):
        """Load current configuration values into the dialog."""
        # Mother model
        mother_model = self.delegation_rules.get('mother_model', 'Auto-Select')
        if mother_model == 'Auto-Select':
            self.mother_model_var.set('Auto-Select')
        else:
            self.mother_model_var.set('Manual')
            self.manual_mother_combo.set(mother_model)
        
        # Task delegation
        task_delegation = self.delegation_rules.get('task_delegation', {})
        for task_key, combos in self.task_combos.items():
            task_config = task_delegation.get(task_key, {})
            for priority, combo in combos.items():
                value = task_config.get(priority, '')
                combo.set(value)
    
    def save_config(self):
        """Save the configuration."""
        try:
            # Get mother model
            if self.mother_model_var.get() == 'Auto-Select':
                mother_model = 'Auto-Select'
            else:
                mother_model = self.manual_mother_combo.get()
            
            # Get task delegation
            task_delegation = {}
            for task_key, combos in self.task_combos.items():
                task_delegation[task_key] = {}
                for priority, combo in combos.items():
                    task_delegation[task_key][priority] = combo.get()
            
            # Save configuration
            config = {
                'mother_model': mother_model,
                'task_delegation': task_delegation,
                'timestamp': time.time()
            }
            
            with open('delegation_config.json', 'w') as f:
                json.dump(config, f, indent=2)
            
            messagebox.showinfo("Configuration Saved", "Model delegation configuration has been saved successfully!")
            
            if self.on_save_callback:
                self.on_save_callback(config)
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save configuration: {str(e)}")
    
    def reset_to_defaults(self):
        """Reset configuration to defaults."""
        if messagebox.askyesno("Reset Configuration", "Reset all settings to defaults?"):
            # Reset mother model
            self.mother_model_var.set('Auto-Select')
            self.manual_mother_combo.set('')
            
            # Reset task delegation
            for task_key, combos in self.task_combos.items():
                for priority, combo in combos.items():
                    combo.set('')
    
    def test_config(self):
        """Test the current configuration."""
        test_requests = [
            ("Write a Python function", "coding"),
            ("Create a poem", "creative_writing"),
            ("Explain AI", "conversation"),
            ("Compare two options", "analysis"),
            ("Solve this puzzle", "reasoning")
        ]
        
        results = []
        for request, expected_task in test_requests:
            # Simulate task analysis
            selected_model = self._get_model_for_task(expected_task, 'primary')
            results.append(f"'{request}' → {selected_model or 'No model assigned'} ({expected_task})")
        
        result_text = "🧪 Configuration Test Results:\n\n" + "\n".join(results)
        messagebox.showinfo("Test Results", result_text)
    
    def _get_model_for_task(self, task_type: str, priority: str) -> str:
        """Get the assigned model for a task type and priority."""
        if task_type in self.task_combos:
            if priority in self.task_combos[task_type]:
                return self.task_combos[task_type][priority].get()
        return ""
    
    def close_dialog(self):
        """Close the dialog."""
        self.dialog.destroy()

def show_model_config_dialog(parent, available_models: List[str], on_save_callback: Optional[Callable] = None):
    """Show the model configuration dialog."""
    return ModelConfigDialog(parent, available_models, on_save_callback)
