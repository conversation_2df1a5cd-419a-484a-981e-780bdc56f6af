#!/usr/bin/env python3
"""
🤖 AI Assistant - Consolidated Application
Autonomous, Intelligent, Multi-Model AI Assistant with Voice Control

Features:
- Intelligent model delegation (mother model system)
- Voice interaction with wake word detection
- Autonomous task execution (screen automation, file operations)
- Model benchmarking and performance optimization
- Real-time conversation with 25+ models
- File operations and code extraction
- Comprehensive GUI with all features integrated

Author: AI Assistant Development Team
Version: 2.0 - Consolidated Edition
"""

import sys
import os
import threading
import time
from typing import Dict, List, Any, Optional

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Core imports
from core.config import get_config, save_config
from core.ai_assistant import get_ai_assistant
from core.model_manager import get_model_manager
from core.autonomous_system import get_autonomous_system

# GUI imports (with fallback)
try:
    from gui.main_window import MainWindow
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"GUI not available: {str(e)}")
    GUI_AVAILABLE = False

class AIAssistantApp:
    """Main application class that coordinates all components."""
    
    def __init__(self):
        print("🤖 Initializing AI Assistant...")
        
        # Core components
        self.config = get_config()
        self.ai_assistant = get_ai_assistant()
        self.model_manager = get_model_manager()
        self.autonomous_system = get_autonomous_system()
        
        # GUI
        self.gui: Optional[MainWindow] = None
        
        # State
        self.is_running = False
        self.current_model = "phi3:mini"
        
        print("✅ AI Assistant initialized successfully")
    
    def initialize_gui(self) -> bool:
        """Initialize the GUI."""
        if not GUI_AVAILABLE:
            print("❌ GUI not available")
            return False
        
        try:
            print("🖥️ Initializing GUI...")
            
            self.gui = MainWindow()
            
            # Set up GUI callbacks
            self.gui.send_callback = self.handle_user_message
            self.gui.model_change_callback = self.handle_model_change
            self.gui.new_chat_callback = self.handle_new_chat
            self.gui.voice_toggle_callback = self.handle_voice_toggle
            self.gui.autonomous_task_callback = self.handle_autonomous_task
            self.gui.benchmark_model_callback = self.handle_benchmark_model
            self.gui.config_callback = self.handle_configuration
            self.gui.file_operations_callback = self.handle_file_operations
            
            print("✅ GUI initialized successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize GUI: {str(e)}")
            return False
    
    def start(self) -> bool:
        """Start the application."""
        print("🚀 Starting AI Assistant...")
        
        # Test Ollama connection
        success, message = self.ai_assistant.test_connection()
        if not success:
            print(f"❌ Failed to connect to Ollama: {message}")
            print("Please ensure Ollama is running and accessible.")
            return False
        
        print("✅ Connected to Ollama successfully")
        
        # Get available models
        models = self.ai_assistant.get_available_models()
        if not models:
            print("⚠️ No models found. Please install at least one model.")
            print("Example: ollama pull phi3:mini")
            return False
        
        print(f"📋 Found {len(models)} available models")
        
        # Set default model
        if self.current_model in models:
            self.ai_assistant.set_model(self.current_model)
        else:
            self.current_model = models[0]
            self.ai_assistant.set_model(self.current_model)
        
        print(f"🎯 Using model: {self.current_model}")
        
        # Initialize GUI if available
        if GUI_AVAILABLE:
            if not self.initialize_gui():
                return False
            
            # Update GUI with initial data
            self.gui.update_models(models)
            self.gui.update_status(f"Connected to Ollama - {len(models)} models available")
            self.gui.add_message("System", f"🤖 AI Assistant ready! Using {self.current_model}", "system")
            self.gui.add_message("System", "💡 Try: 'Write me a poem' or click 🤖 Auto for autonomous tasks", "system")
            
            # Set up status update timer
            self.setup_status_updates()
        
        self.is_running = True
        print("🎉 AI Assistant started successfully!")
        
        return True
    
    def setup_status_updates(self):
        """Set up periodic status updates for GUI."""
        def update_status():
            while self.is_running and self.gui:
                try:
                    # Update autonomous status
                    auto_status = self.autonomous_system.get_status()
                    self.gui.update_autonomous_status(auto_status)
                    
                    # Update voice status
                    voice_status = {
                        'enabled': auto_status.get('voice_enabled', False),
                        'listening': auto_status.get('voice_listening', False),
                        'speaking': auto_status.get('voice_speaking', False)
                    }
                    self.gui.update_voice_status(voice_status)
                    
                    time.sleep(1)  # Update every second
                    
                except Exception as e:
                    print(f"Status update error: {str(e)}")
                    break
        
        status_thread = threading.Thread(target=update_status, daemon=True)
        status_thread.start()
    
    def run(self):
        """Run the application."""
        if not self.start():
            return False
        
        try:
            if GUI_AVAILABLE and self.gui:
                # Run GUI main loop
                self.gui.run()
            else:
                # Run console mode
                self.run_console_mode()
                
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
        except Exception as e:
            print(f"❌ Application error: {str(e)}")
        finally:
            self.shutdown()
        
        return True
    
    def run_console_mode(self):
        """Run in console mode without GUI."""
        print("\n💬 Console Mode - Type 'quit' to exit")
        print("Available commands:")
        print("  - Any message: Chat with AI")
        print("  - 'voice': Toggle voice mode")
        print("  - 'models': List available models")
        print("  - 'switch <model>': Switch to different model")
        print("  - 'benchmark <model>': Benchmark a model")
        print("  - 'quit': Exit application")
        
        while self.is_running:
            try:
                user_input = input(f"\n[{self.current_model}] You: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'voice':
                    self.handle_voice_toggle()
                elif user_input.lower() == 'models':
                    models = self.ai_assistant.get_available_models()
                    print(f"Available models: {', '.join(models)}")
                elif user_input.startswith('switch '):
                    model_name = user_input[7:].strip()
                    self.handle_model_change(model_name)
                elif user_input.startswith('benchmark '):
                    model_name = user_input[10:].strip()
                    self.handle_benchmark_model(model_name)
                elif user_input:
                    self.handle_user_message(user_input)
                    
            except EOFError:
                break
            except Exception as e:
                print(f"Error: {str(e)}")
    
    # Event handlers
    def handle_user_message(self, message: str):
        """Handle user message."""
        def on_response(response: str):
            if self.gui:
                self.gui.add_message("AI Assistant", response, "ai")
            else:
                print(f"AI: {response}")
        
        def on_error(error: str):
            if self.gui:
                self.gui.add_message("System", f"❌ Error: {error}", "error")
            else:
                print(f"Error: {error}")
        
        # Add user message to GUI
        if self.gui:
            self.gui.add_message("You", message, "user")
            self.gui.update_status("AI is thinking...")
        
        # Process with AI assistant
        self.ai_assistant.process_user_input(
            message, on_response, on_error, 
            use_streaming=self.config.enable_streaming
        )
        
        # Update status
        if self.gui:
            self.gui.update_status("Ready")
    
    def handle_model_change(self, model_name: str):
        """Handle model change."""
        success, message = self.ai_assistant.set_model(model_name)
        
        if success:
            self.current_model = model_name
            status_msg = f"✅ Switched to {model_name}"
            print(status_msg)
            
            if self.gui:
                self.gui.add_message("System", status_msg, "system")
                self.gui.update_status(f"Using {model_name}")
        else:
            error_msg = f"❌ Failed to switch to {model_name}: {message}"
            print(error_msg)
            
            if self.gui:
                self.gui.add_message("System", error_msg, "error")
    
    def handle_new_chat(self):
        """Handle new chat creation."""
        from chat_session_manager import session_manager
        session_manager.create_new_session(self.current_model, "New Chat")
        
        if self.gui:
            self.gui.add_message("System", "🆕 New chat session started", "system")
        
        print("New chat session created")
    
    def handle_voice_toggle(self):
        """Handle voice mode toggle."""
        if not self.autonomous_system.is_available():
            error_msg = "❌ Voice features not available. Install voice dependencies."
            print(error_msg)
            
            if self.gui:
                self.gui.add_message("System", error_msg, "error")
            return
        
        # Get current voice status
        status = self.autonomous_system.get_status()
        voice_enabled = status.get('voice_enabled', False)
        
        if voice_enabled:
            self.ai_assistant.disable_voice_mode()
            msg = "🔇 Voice mode disabled"
        else:
            success = self.ai_assistant.enable_voice_mode()
            if success:
                msg = "🎤 Voice mode enabled - Say 'Hey Assistant' to get my attention"
            else:
                msg = "❌ Failed to enable voice mode"
        
        print(msg)
        if self.gui:
            self.gui.add_message("System", msg, "system")
    
    def handle_autonomous_task(self, task_description: str):
        """Handle autonomous task execution."""
        if not self.autonomous_system.is_available():
            error_msg = "❌ Autonomous features not available. Install autonomous dependencies."
            print(error_msg)
            
            if self.gui:
                self.gui.add_message("System", error_msg, "error")
            return
        
        success = self.ai_assistant.execute_autonomous_task(task_description)
        
        if success:
            msg = f"🤖 Starting autonomous task: {task_description}"
        else:
            msg = f"❌ Failed to start autonomous task: {task_description}"
        
        print(msg)
        if self.gui:
            self.gui.add_message("System", msg, "system")
    
    def handle_benchmark_model(self, model_name: str):
        """Handle model benchmarking."""
        if self.gui:
            self.gui.show_benchmark_dialog(model_name)
        else:
            print(f"🧪 Benchmarking {model_name}...")
            
            def progress_callback(current, total, status):
                print(f"Progress: {current}/{total} - {status}")
            
            results = self.model_manager.benchmark_model(model_name, progress_callback=progress_callback)
            
            if results:
                print(f"✅ Benchmark completed for {model_name}")
                print(f"Average response time: {sum(r.response_time for r in results) / len(results):.2f}s")
            else:
                print(f"❌ Benchmark failed for {model_name}")
    
    def handle_configuration(self):
        """Handle configuration dialog."""
        if self.gui:
            self.gui.show_config_dialog()
        else:
            print("Configuration is available in GUI mode only")
    
    def handle_file_operations(self, operation: str):
        """Handle file operations."""
        try:
            if operation.startswith("load:"):
                file_path = operation[5:]
                success = self.ai_assistant.load_file(file_path)
                
                if success:
                    msg = f"📁 Loaded file: {file_path}"
                else:
                    msg = f"❌ Failed to load file: {file_path}"
                
                print(msg)
                if self.gui:
                    self.gui.add_message("System", msg, "system")
            
        except Exception as e:
            error_msg = f"❌ File operation failed: {str(e)}"
            print(error_msg)
            
            if self.gui:
                self.gui.add_message("System", error_msg, "error")
    
    def shutdown(self):
        """Shutdown the application."""
        print("🛑 Shutting down AI Assistant...")
        
        self.is_running = False
        
        # Stop autonomous system
        if self.autonomous_system:
            self.autonomous_system.stop_all()
        
        # Save configuration
        save_config()
        
        print("✅ AI Assistant shutdown complete")

def main():
    """Main entry point."""
    print("🤖 AI Assistant - Consolidated Edition")
    print("=" * 50)
    
    try:
        app = AIAssistantApp()
        return app.run()
        
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
