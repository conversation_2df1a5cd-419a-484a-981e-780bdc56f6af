#!/usr/bin/env python3
"""Simple model testing to create basic delegation system."""

import time
import json
from enhanced_ollama_api import EnhancedOllamaAPI

def test_simple_models():
    """Test a few key models with simple prompts."""
    ollama = EnhancedOllamaAPI()
    
    # Test connection
    success, message = ollama.test_connection()
    if not success:
        print(f"❌ Cannot connect to Ollama: {message}")
        return
    
    # Get simple model names using ollama list command
    import subprocess
    try:
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            simple_models = []
            for line in lines:
                if line.strip():
                    model_name = line.split()[0]
                    simple_models.append(model_name)
        else:
            print("❌ Failed to get model list")
            return
    except Exception as e:
        print(f"❌ Error getting models: {str(e)}")
        return
    
    print(f"🤖 Found {len(simple_models)} models:")
    for model in simple_models[:10]:  # Show first 10
        print(f"   • {model}")
    
    # Test prompts
    test_prompts = {
        "coding": "Write a simple Python function to add two numbers.",
        "creative": "Write a short poem about AI.",
        "conversation": "What is artificial intelligence?",
        "analysis": "Compare Python and JavaScript."
    }
    
    results = {}
    
    print("\n🧪 Testing key models...")
    
    # Test a few representative models
    test_models = []
    for model in simple_models:
        if any(name in model.lower() for name in ['phi3', 'llama2', 'mistral', 'codellama', 'gemma']):
            test_models.append(model)
            if len(test_models) >= 5:  # Test max 5 models
                break
    
    if not test_models:
        test_models = simple_models[:3]  # Fallback to first 3
    
    for model_name in test_models:
        print(f"\n📝 Testing {model_name}...")
        model_results = {}
        
        for task_type, prompt in test_prompts.items():
            try:
                # Switch to model
                success, message = ollama.set_model(model_name)
                if not success:
                    print(f"    ❌ Failed to switch to {model_name}")
                    continue
                
                # Time the response
                start_time = time.time()
                success, response = ollama.generate_sync(prompt)
                end_time = time.time()
                
                if success and response:
                    response_time = end_time - start_time
                    response_length = len(response)
                    
                    # Simple scoring
                    quality_score = min(10, len(response) / 50)  # Length-based quality
                    speed_score = max(1, min(10, 10 - response_time))  # Speed score
                    
                    model_results[task_type] = {
                        'response_time': response_time,
                        'quality_score': quality_score,
                        'speed_score': speed_score,
                        'response_length': response_length
                    }
                    
                    print(f"    ✅ {task_type}: {response_time:.2f}s, Q:{quality_score:.1f}, S:{speed_score:.1f}")
                else:
                    print(f"    ❌ {task_type}: Failed to get response")
                    
            except Exception as e:
                print(f"    ❌ {task_type}: Error - {str(e)}")
        
        if model_results:
            results[model_name] = model_results
    
    # Create simple delegation rules
    delegation_rules = {}
    
    if results:
        print("\n📊 Creating delegation rules...")
        
        # Find best model for each task type
        for task_type in test_prompts.keys():
            best_model = None
            best_score = 0
            
            for model_name, model_data in results.items():
                if task_type in model_data:
                    # Combined score: quality + speed
                    score = (model_data[task_type]['quality_score'] + 
                            model_data[task_type]['speed_score']) / 2
                    
                    if score > best_score:
                        best_score = score
                        best_model = model_name
            
            if best_model:
                delegation_rules[task_type] = best_model
                print(f"   🎯 {task_type}: {best_model} (score: {best_score:.1f})")
        
        # Save simple delegation rules
        simple_delegation = {
            'delegation_rules': delegation_rules,
            'model_results': results,
            'timestamp': time.time()
        }
        
        with open('simple_delegation.json', 'w') as f:
            json.dump(simple_delegation, f, indent=2)
        
        print(f"\n💾 Saved delegation rules to simple_delegation.json")
        
        # Create fallback rules for common patterns
        fallback_rules = {
            'coding': delegation_rules.get('coding', 'phi3:mini'),
            'creative_writing': delegation_rules.get('creative', 'phi3:mini'),
            'conversation': delegation_rules.get('conversation', 'phi3:mini'),
            'analysis': delegation_rules.get('analysis', 'phi3:mini'),
            'fast': min(results.keys(), key=lambda x: min([results[x][t]['response_time'] for t in results[x]]) if results[x] else 999) if results else 'phi3:mini'
        }
        
        print("\n🎯 Delegation Summary:")
        for task, model in fallback_rules.items():
            print(f"   {task}: {model}")
        
        return fallback_rules
    
    else:
        print("❌ No successful tests completed")
        return None

if __name__ == "__main__":
    print("🚀 Simple Model Testing for Delegation")
    print("=" * 50)
    
    rules = test_simple_models()
    
    if rules:
        print("\n✅ Basic delegation system created!")
        print("Your AI will now use different models for different tasks.")
    else:
        print("\n❌ Failed to create delegation system")
        print("Check that Ollama is running and models are available.")
