#!/usr/bin/env python3
"""Smart model delegation system based on model characteristics."""

import json
import os
from typing import Dict, List, Optional

class SmartModelDelegator:
    """Intelligent model delegation based on model names and characteristics."""
    
    def __init__(self):
        self.delegation_rules = self._create_smart_rules()
        self.available_models = self._get_available_models()
        self.load_test_results()
    
    def _get_available_models(self) -> List[str]:
        """Get available models from ollama."""
        try:
            import subprocess
            result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                models = []
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        models.append(model_name)
                return models
        except Exception:
            pass
        return []
    
    def _create_smart_rules(self) -> Dict[str, Dict[str, List[str]]]:
        """Create smart delegation rules based on model names and known capabilities."""
        return {
            'coding': {
                'primary': ['deepseek-coder:6.7b', 'codellama:7b', 'deepseek-r1:8b', 'deepcoder:1.5b'],
                'secondary': ['mistral:7b', 'phi4-mini-reasoning:latest', 'qwen3:4b'],
                'fast': ['phi3:mini', 'deepcoder:1.5b', 'qwen3:0.6b']
            },
            'creative_writing': {
                'primary': ['llama2:7b', 'mistral:7b', 'gemma3:12b-it-qat', 'qwen3:latest'],
                'secondary': ['neural-chat:7b', 'gemma3:4b', 'llama3.2:latest'],
                'fast': ['phi3:mini', 'gemma3:1b-it-qat', 'qwen3:0.6b']
            },
            'conversation': {
                'primary': ['neural-chat:7b', 'mistral:7b', 'llama2:7b', 'gemma3:4b'],
                'secondary': ['phi3:mini', 'qwen3:4b', 'llama3.2:latest'],
                'fast': ['phi3:mini', 'gemma3:1b-it-qat', 'qwen3:0.6b']
            },
            'analysis': {
                'primary': ['deepseek-r1:8b', 'mistral:7b', 'gemma3:12b-it-qat', 'qwen3:latest'],
                'secondary': ['phi4-mini-reasoning:latest', 'neural-chat:7b', 'gemma3:4b'],
                'fast': ['phi3:mini', 'qwen3:4b', 'gemma3:1b-it-qat']
            },
            'reasoning': {
                'primary': ['deepseek-r1:8b', 'deepseek-r1:7b', 'phi4-mini-reasoning:latest'],
                'secondary': ['mistral:7b', 'qwen3:latest', 'gemma3:12b-it-qat'],
                'fast': ['phi3:mini', 'qwen3:4b', 'deepseek-r1:1.5b']
            },
            'vision': {
                'primary': ['llava:latest', 'qwen2.5vl:3b'],
                'secondary': ['gemma3n:latest'],
                'fast': ['qwen2.5vl:3b']
            }
        }
    
    def load_test_results(self):
        """Load test results if available."""
        try:
            if os.path.exists('simple_delegation.json'):
                with open('simple_delegation.json', 'r') as f:
                    data = json.load(f)
                    test_rules = data.get('delegation_rules', {})
                    
                    # Update rules with test results
                    for task_type, best_model in test_rules.items():
                        if task_type in self.delegation_rules:
                            # Move tested best model to front of primary list
                            if best_model in self.available_models:
                                for priority in ['primary', 'secondary', 'fast']:
                                    if best_model in self.delegation_rules[task_type][priority]:
                                        self.delegation_rules[task_type][priority].remove(best_model)
                                self.delegation_rules[task_type]['primary'].insert(0, best_model)
                    
                    print("✅ Loaded test results and updated delegation rules")
        except Exception as e:
            print(f"ℹ️ No test results loaded: {str(e)}")
    
    def get_best_model(self, task_type: str, prioritize_speed: bool = False) -> str:
        """Get the best available model for a task type."""
        # Map common task types
        task_mapping = {
            'coding': 'coding',
            'creative_writing': 'creative_writing',
            'creative': 'creative_writing',
            'conversation': 'conversation',
            'chat': 'conversation',
            'analysis': 'analysis',
            'problem_solving': 'reasoning',
            'reasoning': 'reasoning',
            'vision': 'vision',
            'image': 'vision'
        }
        
        mapped_task = task_mapping.get(task_type, 'conversation')
        
        if mapped_task not in self.delegation_rules:
            return self._get_fallback_model()
        
        # Choose priority level based on speed requirement
        if prioritize_speed:
            priority_order = ['fast', 'secondary', 'primary']
        else:
            priority_order = ['primary', 'secondary', 'fast']
        
        # Find first available model
        for priority in priority_order:
            for model in self.delegation_rules[mapped_task][priority]:
                if model in self.available_models:
                    return model
        
        return self._get_fallback_model()
    
    def _get_fallback_model(self) -> str:
        """Get a fallback model when no specific model is found."""
        fallback_order = [
            'phi3:mini', 'mistral:7b', 'llama2:7b', 'neural-chat:7b',
            'gemma3:4b', 'qwen3:4b', 'deepseek-r1:1.5b'
        ]
        
        for model in fallback_order:
            if model in self.available_models:
                return model
        
        # Ultimate fallback - return first available model
        if self.available_models:
            return self.available_models[0]
        
        return 'phi3:mini'  # Default if nothing else works
    
    def analyze_request(self, request: str) -> tuple[str, bool]:
        """Analyze a request to determine task type and speed priority."""
        request_lower = request.lower()
        
        # Determine task type
        if any(word in request_lower for word in [
            'code', 'program', 'script', 'function', 'algorithm', 'debug',
            'python', 'javascript', 'java', 'c++', 'html', 'css', 'sql'
        ]):
            task_type = 'coding'
        elif any(word in request_lower for word in [
            'poem', 'story', 'creative', 'write', 'compose', 'letter',
            'essay', 'article', 'blog', 'narrative', 'fiction'
        ]):
            task_type = 'creative_writing'
        elif any(word in request_lower for word in [
            'analyze', 'compare', 'evaluate', 'assess', 'examine',
            'study', 'research', 'investigate', 'review'
        ]):
            task_type = 'analysis'
        elif any(word in request_lower for word in [
            'solve', 'problem', 'puzzle', 'logic', 'reasoning',
            'think', 'figure out', 'work out'
        ]):
            task_type = 'reasoning'
        elif any(word in request_lower for word in [
            'image', 'picture', 'photo', 'visual', 'see', 'look'
        ]):
            task_type = 'vision'
        else:
            task_type = 'conversation'
        
        # Determine speed priority
        prioritize_speed = any(word in request_lower for word in [
            'quick', 'fast', 'rapid', 'immediate', 'urgent', 'asap'
        ])
        
        return task_type, prioritize_speed
    
    def delegate_request(self, request: str) -> tuple[str, str]:
        """Delegate a request to the best model."""
        task_type, prioritize_speed = self.analyze_request(request)
        best_model = self.get_best_model(task_type, prioritize_speed)
        
        return best_model, task_type
    
    def get_delegation_summary(self) -> Dict[str, str]:
        """Get a summary of current delegation rules."""
        summary = {}
        for task_type in self.delegation_rules.keys():
            best_model = self.get_best_model(task_type, False)
            fast_model = self.get_best_model(task_type, True)
            summary[task_type] = {
                'best': best_model,
                'fastest': fast_model
            }
        return summary
    
    def print_delegation_summary(self):
        """Print delegation summary."""
        print("\n🎯 SMART MODEL DELEGATION SUMMARY")
        print("=" * 50)
        
        summary = self.get_delegation_summary()
        for task_type, models in summary.items():
            print(f"{task_type.upper():15} → Best: {models['best']:20} | Fast: {models['fastest']}")
        
        print(f"\n📊 Available Models: {len(self.available_models)}")
        print("🧠 Delegation based on model names and capabilities")

# Global delegator instance
smart_delegator = SmartModelDelegator()

def get_best_model_for_request(request: str) -> tuple[str, str]:
    """Get the best model for a specific request."""
    return smart_delegator.delegate_request(request)

def get_delegation_summary() -> Dict[str, str]:
    """Get delegation summary."""
    return smart_delegator.get_delegation_summary()

if __name__ == "__main__":
    print("🧠 Smart Model Delegation System")
    print("=" * 40)
    
    delegator = SmartModelDelegator()
    delegator.print_delegation_summary()
    
    print("\n🧪 Test Examples:")
    test_requests = [
        "Write a Python function to sort a list",
        "Create a poem about technology", 
        "Explain quantum computing",
        "Quick answer: what is AI?",
        "Analyze the pros and cons of remote work"
    ]
    
    for request in test_requests:
        model, task_type = delegator.delegate_request(request)
        print(f"'{request}' → {model} ({task_type})")
