#!/usr/bin/env python3
"""Voice interface module for speech recognition and text-to-speech."""

import threading
import time
import queue
import logging
from typing import Callable, Optional, Dict, List
from dataclasses import dataclass

# Voice dependencies with fallback handling
try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    sr = None

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    pyttsx3 = None

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    pyaudio = None


@dataclass
class VoiceSettings:
    """Voice configuration settings."""
    # Speech Recognition
    wake_word: str = "hey assistant"
    language: str = "en-US"
    timeout: float = 1.0
    phrase_timeout: float = 0.3
    energy_threshold: int = 300
    
    # Text-to-Speech
    voice_id: int = 0  # 0 for first available voice
    speech_rate: int = 200  # Words per minute
    volume: float = 0.9  # 0.0 to 1.0
    
    # Behavior
    continuous_listening: bool = False
    push_to_talk: bool = False
    auto_adjust_noise: bool = True


class VoiceInterface:
    """Handle voice recognition and text-to-speech functionality."""
    
    def __init__(self, settings: VoiceSettings = None):
        self.settings = settings or VoiceSettings()
        self.is_listening = False
        self.is_speaking = False
        self.recognition_active = False
        
        # Callbacks
        self.on_speech_recognized: Optional[Callable[[str], None]] = None
        self.on_wake_word_detected: Optional[Callable[[], None]] = None
        self.on_listening_started: Optional[Callable[[], None]] = None
        self.on_listening_stopped: Optional[Callable[[], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        
        # Threading
        self.listen_thread: Optional[threading.Thread] = None
        self.speech_queue = queue.Queue()
        self.stop_listening = threading.Event()
        
        # Initialize components
        self.recognizer = None
        self.microphone = None
        self.tts_engine = None
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize speech recognition and TTS components."""
        try:
            # Initialize speech recognition
            if SPEECH_RECOGNITION_AVAILABLE:
                self.recognizer = sr.Recognizer()
                
                if PYAUDIO_AVAILABLE:
                    self.microphone = sr.Microphone()
                    
                    # Adjust for ambient noise
                    if self.settings.auto_adjust_noise:
                        with self.microphone as source:
                            self.recognizer.adjust_for_ambient_noise(source, duration=1)
                    
                    # Set energy threshold
                    self.recognizer.energy_threshold = self.settings.energy_threshold
                    self.recognizer.timeout = self.settings.timeout
                    self.recognizer.phrase_timeout = self.settings.phrase_timeout
            
            # Initialize text-to-speech
            if TTS_AVAILABLE:
                self.tts_engine = pyttsx3.init()
                self._configure_tts()
                
        except Exception as e:
            self._handle_error(f"Failed to initialize voice components: {str(e)}")
    
    def _configure_tts(self):
        """Configure text-to-speech settings."""
        if not self.tts_engine:
            return
        
        try:
            # Set voice
            voices = self.tts_engine.getProperty('voices')
            if voices and len(voices) > self.settings.voice_id:
                self.tts_engine.setProperty('voice', voices[self.settings.voice_id].id)
            
            # Set rate and volume
            self.tts_engine.setProperty('rate', self.settings.speech_rate)
            self.tts_engine.setProperty('volume', self.settings.volume)
            
        except Exception as e:
            self._handle_error(f"Failed to configure TTS: {str(e)}")
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get list of available TTS voices."""
        if not self.tts_engine:
            return []
        
        try:
            voices = self.tts_engine.getProperty('voices')
            return [
                {
                    'id': i,
                    'name': voice.name,
                    'language': getattr(voice, 'languages', ['Unknown'])[0] if hasattr(voice, 'languages') else 'Unknown'
                }
                for i, voice in enumerate(voices)
            ]
        except Exception:
            return []
    
    def set_voice(self, voice_id: int):
        """Change TTS voice."""
        self.settings.voice_id = voice_id
        self._configure_tts()
    
    def set_speech_rate(self, rate: int):
        """Change speech rate (words per minute)."""
        self.settings.speech_rate = rate
        if self.tts_engine:
            self.tts_engine.setProperty('rate', rate)
    
    def set_volume(self, volume: float):
        """Change speech volume (0.0 to 1.0)."""
        self.settings.volume = max(0.0, min(1.0, volume))
        if self.tts_engine:
            self.tts_engine.setProperty('volume', self.settings.volume)
    
    def speak(self, text: str, interrupt_current: bool = False):
        """Convert text to speech."""
        if not self.tts_engine:
            self._handle_error("Text-to-speech not available")
            return
        
        if interrupt_current and self.is_speaking:
            self.stop_speaking()
        
        def speak_thread():
            try:
                self.is_speaking = True
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except Exception as e:
                self._handle_error(f"TTS error: {str(e)}")
            finally:
                self.is_speaking = False
        
        threading.Thread(target=speak_thread, daemon=True).start()
    
    def stop_speaking(self):
        """Stop current speech."""
        if self.tts_engine and self.is_speaking:
            try:
                self.tts_engine.stop()
                self.is_speaking = False
            except Exception as e:
                self._handle_error(f"Error stopping speech: {str(e)}")
    
    def start_listening(self, continuous: bool = None):
        """Start voice recognition."""
        if not self.recognizer or not self.microphone:
            self._handle_error("Speech recognition not available")
            return False
        
        if self.is_listening:
            return True
        
        continuous = continuous if continuous is not None else self.settings.continuous_listening
        
        self.stop_listening.clear()
        self.is_listening = True
        self.recognition_active = True
        
        if continuous:
            self.listen_thread = threading.Thread(target=self._continuous_listen, daemon=True)
        else:
            self.listen_thread = threading.Thread(target=self._single_listen, daemon=True)
        
        self.listen_thread.start()
        
        if self.on_listening_started:
            self.on_listening_started()
        
        return True
    
    def stop_listening_now(self):
        """Stop voice recognition."""
        self.stop_listening.set()
        self.is_listening = False
        self.recognition_active = False
        
        if self.on_listening_stopped:
            self.on_listening_stopped()
    
    def _continuous_listen(self):
        """Continuous listening loop with wake word detection."""
        while not self.stop_listening.is_set() and self.recognition_active:
            try:
                with self.microphone as source:
                    # Listen for audio
                    audio = self.recognizer.listen(source, timeout=1, phrase_timeout=self.settings.phrase_timeout)
                
                # Recognize speech
                text = self.recognizer.recognize_google(audio, language=self.settings.language).lower()
                
                if self.settings.wake_word.lower() in text:
                    if self.on_wake_word_detected:
                        self.on_wake_word_detected()
                    
                    # Continue listening for command after wake word
                    self._listen_for_command()
                
            except sr.WaitTimeoutError:
                continue
            except sr.UnknownValueError:
                continue
            except sr.RequestError as e:
                self._handle_error(f"Speech recognition error: {str(e)}")
                break
            except Exception as e:
                self._handle_error(f"Listening error: {str(e)}")
                break
        
        self.is_listening = False
        self.recognition_active = False
    
    def _single_listen(self):
        """Single listening session."""
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=5, phrase_timeout=self.settings.phrase_timeout)
            
            text = self.recognizer.recognize_google(audio, language=self.settings.language)
            
            if self.on_speech_recognized:
                self.on_speech_recognized(text)
                
        except sr.WaitTimeoutError:
            self._handle_error("Listening timeout")
        except sr.UnknownValueError:
            self._handle_error("Could not understand audio")
        except sr.RequestError as e:
            self._handle_error(f"Speech recognition error: {str(e)}")
        except Exception as e:
            self._handle_error(f"Listening error: {str(e)}")
        finally:
            self.is_listening = False
            self.recognition_active = False
    
    def _listen_for_command(self):
        """Listen for command after wake word detection."""
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=5, phrase_timeout=1.0)
            
            text = self.recognizer.recognize_google(audio, language=self.settings.language)
            
            if self.on_speech_recognized:
                self.on_speech_recognized(text)
                
        except sr.WaitTimeoutError:
            pass  # No command given, continue listening
        except sr.UnknownValueError:
            pass  # Couldn't understand, continue listening
        except Exception as e:
            self._handle_error(f"Command recognition error: {str(e)}")
    
    def _handle_error(self, error_message: str):
        """Handle errors."""
        if self.on_error:
            self.on_error(error_message)
        else:
            print(f"Voice Interface Error: {error_message}")
    
    def is_available(self) -> Dict[str, bool]:
        """Check availability of voice components."""
        return {
            'speech_recognition': SPEECH_RECOGNITION_AVAILABLE and self.recognizer is not None,
            'microphone': PYAUDIO_AVAILABLE and self.microphone is not None,
            'text_to_speech': TTS_AVAILABLE and self.tts_engine is not None
        }
    
    def get_status(self) -> Dict[str, bool]:
        """Get current status."""
        return {
            'is_listening': self.is_listening,
            'is_speaking': self.is_speaking,
            'recognition_active': self.recognition_active
        }


# Global voice interface instance
voice_interface = VoiceInterface()

def initialize_voice(settings: VoiceSettings = None) -> bool:
    """Initialize voice interface with settings."""
    global voice_interface
    voice_interface = VoiceInterface(settings)
    availability = voice_interface.is_available()
    return any(availability.values())

def speak(text: str, interrupt: bool = False):
    """Speak text using TTS."""
    voice_interface.speak(text, interrupt)

def start_listening(continuous: bool = None) -> bool:
    """Start voice recognition."""
    return voice_interface.start_listening(continuous)

def stop_listening():
    """Stop voice recognition."""
    voice_interface.stop_listening_now()

def get_available_voices() -> List[Dict[str, str]]:
    """Get available TTS voices."""
    return voice_interface.get_available_voices()
