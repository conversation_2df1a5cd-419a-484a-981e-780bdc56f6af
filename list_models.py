#!/usr/bin/env python3
"""List all available models for testing."""

from enhanced_ollama_api import EnhancedOllamaAPI

def main():
    """List all available models."""
    print("🤖 Available Models for Testing")
    print("=" * 50)
    
    ollama = EnhancedOllamaAPI()
    
    # Test connection
    success, message = ollama.test_connection()
    if not success:
        print(f"❌ Cannot connect to Ollama: {message}")
        print("\nPlease make sure:")
        print("1. Ollama is installed")
        print("2. Ollama service is running (ollama serve)")
        print("3. At least one model is downloaded")
        return
    
    # Get available models
    models = ollama.get_available_models()
    
    if not models:
        print("❌ No models found")
        print("Download a model with: ollama pull phi3:mini")
        return
    
    print(f"✅ Found {len(models)} models:")
    print()
    
    for i, model in enumerate(models, 1):
        print(f"{i:2d}. {model}")
    
    print(f"\n📊 Ready to test {len(models)} models!")
    print("Run: python test_all_models.py")

if __name__ == "__main__":
    main()
