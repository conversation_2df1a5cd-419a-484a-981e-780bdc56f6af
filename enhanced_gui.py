"""Enhanced GUI with multi-model support and tkinter fixes."""

import os
import sys
from tkinter_fix import tkinter_manager
from typing import Callable, Optional, List, Dict
from datetime import datetime
from enhanced_config import *


class EnhancedGUI:
    """Enhanced GUI with model selection and modern interface."""
    
    def __init__(self):
        # Initialize tkinter with fixes
        self.tk, error = tkinter_manager.get_tkinter()
        if not self.tk:
            raise RuntimeError(f"Cannot initialize GUI: {error}")
        
        from tkinter import ttk, scrolledtext, messagebox
        self.ttk = ttk
        self.scrolledtext = scrolledtext
        self.messagebox = messagebox
        
        # Initialize state first
        self.typing_active = False
        self.current_model = "phi3:mini"
        self.available_models = []

        # Performance optimizations
        self._update_pending = False
        self._last_scroll_time = 0
        self._scroll_delay = 0.1  # Limit scroll updates

        # Callbacks
        self.send_callback: Optional[Callable] = None
        self.browse_callback: Optional[Callable] = None
        self.model_change_callback: Optional[Callable] = None
        self.new_chat_callback: Optional[Callable] = None
        self.load_chat_callback: Optional[Callable] = None
        self.analyze_image_callback: Optional[Callable] = None
        self.generate_image_callback: Optional[Callable] = None
        self.screenshot_callback: Optional[Callable] = None

        # Voice and autonomous callbacks
        self.voice_toggle_callback: Optional[Callable] = None
        self.autonomous_task_callback: Optional[Callable] = None
        self.voice_settings_callback: Optional[Callable] = None

        # Model management callbacks
        self.benchmark_model_callback: Optional[Callable] = None
        self.model_config_callback: Optional[Callable] = None

        # Setup GUI
        self.root = None
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.setup_bindings()
    
    def setup_window(self):
        """Configure the main window."""
        self.root = self.tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(MIN_WIDTH, MIN_HEIGHT)
        self.root.configure(bg=COLORS['bg'])
        
        # Center window
        self.center_window()
        
        # Set icon if available
        try:
            if hasattr(sys, '_MEIPASS'):  # PyInstaller
                icon_path = os.path.join(sys._MEIPASS, 'icon.ico')
            else:
                icon_path = 'icon.ico'
            
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass  # Icon not available
    
    def center_window(self):
        """Center the window on screen."""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (WINDOW_WIDTH // 2)
        y = (self.root.winfo_screenheight() // 2) - (WINDOW_HEIGHT // 2)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}+{x}+{y}")
    
    def setup_styles(self):
        """Configure ttk styles for dark theme."""
        style = self.ttk.Style()
        style.theme_use('clam')
        
        # Configure dark theme styles
        style.configure('Dark.TFrame', background=COLORS['bg'])
        style.configure('Sidebar.TFrame', background=COLORS['sidebar_bg'])
        style.configure('Dark.TLabel', background=COLORS['bg'], foreground=COLORS['text'])
        style.configure('Sidebar.TLabel', background=COLORS['sidebar_bg'], foreground=COLORS['text'])
        style.configure('Header.TLabel', background=COLORS['bg'], foreground=COLORS['text'], font=HEADER_FONT)
        
        # Combobox styling
        style.configure('Dark.TCombobox', 
                       fieldbackground=COLORS['input_bg'],
                       background=COLORS['button_bg'],
                       foreground=COLORS['text'],
                       bordercolor=COLORS['border'])
    
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container with sidebar
        main_container = self.ttk.Frame(self.root, style='Dark.TFrame')
        main_container.pack(fill=self.tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create sidebar and main area
        self.create_sidebar(main_container)
        self.create_main_area(main_container)
    
    def create_sidebar(self, parent):
        """Create sidebar with model selection and controls."""
        sidebar = self.ttk.Frame(parent, style='Sidebar.TFrame', width=250)
        sidebar.pack(side=self.tk.LEFT, fill=self.tk.Y, padx=(0, 5))
        sidebar.pack_propagate(False)
        
        # Chat session controls
        session_label = self.ttk.Label(
            sidebar,
            text="💬 Chat Sessions",
            style='Header.TLabel'
        )
        session_label.pack(pady=(10, 5))

        # New chat button
        new_chat_btn = self.ttk.Button(
            sidebar,
            text="➕ New Chat",
            command=self._on_new_chat
        )
        new_chat_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Load chat button
        load_chat_btn = self.ttk.Button(
            sidebar,
            text="📁 Load Chat",
            command=self._on_load_chat
        )
        load_chat_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Current session info
        self.session_info_label = self.ttk.Label(
            sidebar,
            text="Current: Default Chat",
            style='Sidebar.TLabel'
        )
        self.session_info_label.pack(pady=(5, 10), padx=10)

        # Multimodal controls
        multimodal_label = self.ttk.Label(
            sidebar,
            text="🖼️ Multimodal",
            style='Header.TLabel'
        )
        multimodal_label.pack(pady=(10, 5))

        # Image analysis button
        analyze_btn = self.ttk.Button(
            sidebar,
            text="📷 Analyze Image",
            command=self._on_analyze_image
        )
        analyze_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Generate image button
        generate_btn = self.ttk.Button(
            sidebar,
            text="🎨 Generate Image",
            command=self._on_generate_image
        )
        generate_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Screenshot button
        screenshot_btn = self.ttk.Button(
            sidebar,
            text="📸 Screenshot",
            command=self._on_screenshot
        )
        screenshot_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Multimodal toggles
        self.image_analysis_var = self.tk.BooleanVar(value=True)
        self.image_generation_var = self.tk.BooleanVar(value=True)
        self.screen_capture_var = self.tk.BooleanVar(value=True)

        toggle_frame = self.ttk.Frame(sidebar)
        toggle_frame.pack(fill=self.tk.X, padx=10, pady=5)

        analysis_check = self.ttk.Checkbutton(
            toggle_frame,
            text="Image Analysis",
            variable=self.image_analysis_var,
            command=self._toggle_image_analysis
        )
        analysis_check.pack(anchor=self.tk.W)

        generation_check = self.ttk.Checkbutton(
            toggle_frame,
            text="Image Generation",
            variable=self.image_generation_var,
            command=self._toggle_image_generation
        )
        generation_check.pack(anchor=self.tk.W)

        capture_check = self.ttk.Checkbutton(
            toggle_frame,
            text="Screen Capture",
            variable=self.screen_capture_var,
            command=self._toggle_screen_capture
        )
        capture_check.pack(anchor=self.tk.W)

        # Header
        header_label = self.ttk.Label(
            sidebar,
            text="🤖 AI Models",
            style='Header.TLabel'
        )
        header_label.pack(pady=(10, 20))
        
        # Model selection
        model_label = self.ttk.Label(sidebar, text="Current Model:", style='Sidebar.TLabel')
        model_label.pack(anchor=self.tk.W, padx=10)
        
        self.model_var = self.tk.StringVar(value=self.current_model)
        self.model_combo = self.ttk.Combobox(
            sidebar,
            textvariable=self.model_var,
            style='Dark.TCombobox',
            state='readonly',
            width=30
        )
        self.model_combo.pack(pady=(5, 10), padx=10, fill=self.tk.X)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_change)
        
        # Model info
        self.model_info_label = self.ttk.Label(
            sidebar, 
            text="Select a model to see details", 
            style='Sidebar.TLabel',
            wraplength=220,
            justify=self.tk.LEFT
        )
        self.model_info_label.pack(pady=(0, 20), padx=10, anchor=self.tk.W)
        
        # Download models section
        download_label = self.ttk.Label(sidebar, text="Download Models:", style='Sidebar.TLabel')
        download_label.pack(anchor=self.tk.W, padx=10)
        
        # Quick download buttons
        quick_models = ["phi3:mini", "codellama:7b", "llama2:7b"]
        for model in quick_models:
            btn = self.tk.Button(
                sidebar,
                text=f"📥 {model}",
                font=("Segoe UI", 8),
                bg=COLORS['button_bg'],
                fg=COLORS['text'],
                activebackground=COLORS['button_hover'],
                relief=self.tk.FLAT,
                command=lambda m=model: self.download_model(m)
            )
            btn.pack(pady=2, padx=10, fill=self.tk.X)
        
        # Model Management Section
        management_label = self.ttk.Label(
            sidebar,
            text="🧠 Model Management",
            style='Header.TLabel'
        )
        management_label.pack(pady=(20, 5))

        # Benchmark button
        benchmark_btn = self.ttk.Button(
            sidebar,
            text="🧪 Benchmark Model",
            command=self._on_benchmark_model
        )
        benchmark_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Configure delegation button
        config_btn = self.ttk.Button(
            sidebar,
            text="⚙️ Configure Delegation",
            command=self._on_model_config
        )
        config_btn.pack(fill=self.tk.X, padx=10, pady=2)

        # Mother model selection
        mother_label = self.ttk.Label(sidebar, text="Mother Model:", style='Sidebar.TLabel')
        mother_label.pack(anchor=self.tk.W, padx=10, pady=(10, 0))

        self.mother_model_var = self.tk.StringVar(value="Auto-Select")
        self.mother_model_combo = self.ttk.Combobox(
            sidebar,
            textvariable=self.mother_model_var,
            style='Dark.TCombobox',
            state='readonly',
            width=30
        )
        self.mother_model_combo.pack(pady=(2, 5), padx=10, fill=self.tk.X)

        # Connection status
        self.connection_label = self.ttk.Label(
            sidebar,
            text=CONNECTION_CHECKING,
            style='Sidebar.TLabel',
            wraplength=220
        )
        self.connection_label.pack(side=self.tk.BOTTOM, pady=10, padx=10)
    
    def create_main_area(self, parent):
        """Create main chat area."""
        main_frame = self.ttk.Frame(parent, style='Dark.TFrame')
        main_frame.pack(side=self.tk.RIGHT, fill=self.tk.BOTH, expand=True)
        
        # Chat area
        self.create_chat_area(main_frame)
        
        # Input area
        self.create_input_area(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
    
    def create_chat_area(self, parent):
        """Create the chat display area."""
        chat_frame = self.ttk.Frame(parent, style='Dark.TFrame')
        chat_frame.pack(fill=self.tk.BOTH, expand=True, pady=(0, 10))
        
        # Chat display
        self.chat_display = self.scrolledtext.ScrolledText(
            chat_frame,
            wrap=self.tk.WORD,
            font=CHAT_FONT,
            bg=COLORS['chat_bg'],
            fg=COLORS['text'],
            insertbackground=COLORS['text'],
            state=self.tk.DISABLED,
            relief=self.tk.FLAT,
            borderwidth=1,
            highlightthickness=1,
            highlightcolor=COLORS['accent'],
            padx=15,
            pady=15
        )
        self.chat_display.pack(fill=self.tk.BOTH, expand=True)
        
        # Configure text tags
        self.setup_text_tags()
    
    def setup_text_tags(self):
        """Configure text tags for message styling."""
        tags = {
            "user": {"foreground": COLORS['user_msg'], "font": CHAT_FONT + ("bold",)},
            "ai": {"foreground": COLORS['ai_msg'], "font": CHAT_FONT},
            "system": {"foreground": COLORS['system_msg'], "font": CHAT_FONT + ("italic",)},
            "error": {"foreground": COLORS['error_msg'], "font": CHAT_FONT + ("bold",)},
            "success": {"foreground": COLORS['success_msg'], "font": CHAT_FONT + ("bold",)},
            "typing": {"foreground": COLORS['typing'], "font": CHAT_FONT + ("italic",)},
            "code": {"foreground": "#e6db74", "font": ("Consolas", 10), "background": "#3e3e3e"}
        }
        
        for tag, config in tags.items():
            self.chat_display.tag_configure(tag, **config)
    
    def create_input_area(self, parent):
        """Create the message input area."""
        input_frame = self.ttk.Frame(parent, style='Dark.TFrame')
        input_frame.pack(fill=self.tk.X, pady=(0, 10))
        
        # Input text widget
        self.message_input = self.tk.Text(
            input_frame,
            height=3,
            font=INPUT_FONT,
            bg=COLORS['input_bg'],
            fg=COLORS['text'],
            insertbackground=COLORS['text'],
            wrap=self.tk.WORD,
            relief=self.tk.FLAT,
            borderwidth=1,
            highlightthickness=1,
            highlightcolor=COLORS['accent'],
            padx=10,
            pady=8
        )
        self.message_input.pack(side=self.tk.LEFT, fill=self.tk.BOTH, expand=True)
        
        # Buttons frame
        buttons_frame = self.ttk.Frame(input_frame, style='Dark.TFrame')
        buttons_frame.pack(side=self.tk.RIGHT, padx=(10, 0))
        
        # Send button
        self.send_button = self.tk.Button(
            buttons_frame,
            text="Send",
            font=BUTTON_FONT,
            bg=COLORS['button_bg'],
            fg=COLORS['text'],
            activebackground=COLORS['button_hover'],
            relief=self.tk.FLAT,
            borderwidth=0,
            padx=20,
            pady=8,
            command=self.on_send_click
        )
        self.send_button.pack(fill=self.tk.X, pady=(0, 5))
        
        # Browse button
        self.browse_button = self.tk.Button(
            buttons_frame,
            text="Browse File",
            font=BUTTON_FONT,
            bg=COLORS['button_bg'],
            fg=COLORS['text'],
            activebackground=COLORS['button_hover'],
            relief=self.tk.FLAT,
            borderwidth=0,
            padx=20,
            pady=8,
            command=self.on_browse_click
        )
        self.browse_button.pack(fill=self.tk.X, pady=(5, 0))

        # Voice control button
        self.voice_button = self.tk.Button(
            buttons_frame,
            text="🎤 Voice",
            font=BUTTON_FONT,
            bg=COLORS['button_bg'],
            fg=COLORS['text'],
            activebackground=COLORS['button_hover'],
            relief=self.tk.FLAT,
            borderwidth=0,
            padx=20,
            pady=8,
            command=self._on_voice_toggle
        )
        self.voice_button.pack(fill=self.tk.X, pady=(5, 0))

        # Autonomous task button
        self.autonomous_button = self.tk.Button(
            buttons_frame,
            text="🤖 Auto",
            font=BUTTON_FONT,
            bg=COLORS['button_bg'],
            fg=COLORS['text'],
            activebackground=COLORS['button_hover'],
            relief=self.tk.FLAT,
            borderwidth=0,
            padx=20,
            pady=8,
            command=self._on_autonomous_task
        )
        self.autonomous_button.pack(fill=self.tk.X, pady=(5, 0))

    def create_status_bar(self, parent):
        """Create status bar."""
        status_frame = self.ttk.Frame(parent, style='Dark.TFrame')
        status_frame.pack(fill=self.tk.X)
        
        self.status_label = self.ttk.Label(
            status_frame,
            text="Ready",
            style='Dark.TLabel'
        )
        self.status_label.pack(side=self.tk.LEFT)
        
        # Clear chat button
        clear_button = self.tk.Button(
            status_frame,
            text="Clear Chat",
            font=("Segoe UI", 8),
            bg=COLORS['bg'],
            fg=COLORS['text_secondary'],
            activebackground=COLORS['button_bg'],
            relief=self.tk.FLAT,
            borderwidth=0,
            command=self.clear_chat
        )
        clear_button.pack(side=self.tk.RIGHT)

    def setup_bindings(self):
        """Set up keyboard bindings."""
        self.message_input.bind('<Return>', self.on_enter_key)
        self.message_input.bind('<Shift-Return>', self.on_shift_enter)
        self.root.bind('<Control-Return>', lambda e: self.on_send_click())

    def on_enter_key(self, event):
        """Handle Enter key press."""
        if not (event.state & 0x1):  # No Shift key
            self.on_send_click()
            return "break"

    def on_shift_enter(self, event):
        """Handle Shift+Enter (new line)."""
        return None  # Allow default behavior

    def on_send_click(self):
        """Handle send button click."""
        if self.send_callback:
            self.send_callback()

    def on_browse_click(self):
        """Handle browse button click."""
        if self.browse_callback:
            self.browse_callback()

    def on_model_change(self, event=None):
        """Handle model selection change."""
        new_model = self.model_var.get()
        if new_model != self.current_model:
            self.current_model = new_model
            # Update model info display
            self.update_model_info(new_model)
            if self.model_change_callback:
                self.model_change_callback(new_model)

    def _on_new_chat(self):
        """Handle new chat button click."""
        if self.new_chat_callback:
            self.new_chat_callback()

    def _on_load_chat(self):
        """Handle load chat button click."""
        if self.load_chat_callback:
            self.load_chat_callback()

    def update_session_info(self, session_info: dict):
        """Update the session info display."""
        if session_info:
            text = f"Current: {session_info['session_name'][:20]}..."
            self.session_info_label.config(text=text)

    def _on_analyze_image(self):
        """Handle analyze image button click."""
        if self.analyze_image_callback:
            self.analyze_image_callback()

    def _on_generate_image(self):
        """Handle generate image button click."""
        # Get prompt from user
        from tkinter import simpledialog
        prompt = simpledialog.askstring(
            "Generate Image",
            "Enter image description:",
            parent=self.root
        )
        if prompt and self.generate_image_callback:
            self.generate_image_callback(prompt)

    def _on_screenshot(self):
        """Handle screenshot button click."""
        if self.screenshot_callback:
            self.screenshot_callback()

    def _on_voice_toggle(self):
        """Handle voice toggle button click."""
        if self.voice_toggle_callback:
            self.voice_toggle_callback()

    def _on_autonomous_task(self):
        """Handle autonomous task button click."""
        # Show dialog for natural task description
        from tkinter import simpledialog

        task = simpledialog.askstring(
            "🤖 Autonomous Mode",
            "Tell me what you'd like me to do:\n\n" +
            "Examples:\n" +
            "• 'Open PyCharm and write a snake game'\n" +
            "• 'Create a todo list in notepad'\n" +
            "• 'Search for Python tutorials'\n" +
            "• 'Take a screenshot and tell me what you see'\n" +
            "• 'Open calculator'\n\n" +
            "Just describe what you want in natural language:",
            parent=self.root
        )

        if task and self.autonomous_task_callback:
            self.autonomous_task_callback(task)

    def update_voice_button_status(self, is_listening: bool, is_speaking: bool):
        """Update voice button appearance based on status."""
        if is_speaking:
            self.voice_button.config(text="🔊 Speaking", bg=COLORS['accent'])
        elif is_listening:
            self.voice_button.config(text="🎤 Listening", bg=COLORS['success_msg'])
        else:
            self.voice_button.config(text="🎤 Voice", bg=COLORS['button_bg'])

    def update_autonomous_button_status(self, is_active: bool):
        """Update autonomous button appearance based on status."""
        if is_active:
            self.autonomous_button.config(text="🤖 Active", bg=COLORS['accent'])
        else:
            self.autonomous_button.config(text="🤖 Auto", bg=COLORS['button_bg'])

    def _on_benchmark_model(self):
        """Handle benchmark model button click."""
        if self.benchmark_model_callback:
            current_model = self.model_var.get()
            self.benchmark_model_callback(current_model)

    def _on_model_config(self):
        """Handle model configuration button click."""
        if self.model_config_callback:
            self.model_config_callback()

    def update_mother_model_options(self, models: list):
        """Update mother model dropdown options."""
        options = ["Auto-Select"] + models
        self.mother_model_combo['values'] = options

    def get_selected_mother_model(self) -> str:
        """Get the selected mother model."""
        return self.mother_model_var.get()

    def set_mother_model(self, model: str):
        """Set the mother model selection."""
        self.mother_model_var.set(model)

    def _toggle_image_analysis(self):
        """Toggle image analysis capability."""
        from multimodal_handler import multimodal_handler
        multimodal_handler.toggle_capability("image_analysis", self.image_analysis_var.get())

    def _toggle_image_generation(self):
        """Toggle image generation capability."""
        from multimodal_handler import multimodal_handler
        multimodal_handler.toggle_capability("image_generation", self.image_generation_var.get())

    def _toggle_screen_capture(self):
        """Toggle screen capture capability."""
        from multimodal_handler import multimodal_handler
        multimodal_handler.toggle_capability("screen_capture", self.screen_capture_var.get())

    def download_model(self, model_name: str):
        """Handle model download request."""
        if hasattr(self, 'download_callback') and self.download_callback:
            self.download_callback(model_name)

    def set_send_callback(self, callback: Callable):
        """Set callback for send action."""
        self.send_callback = callback

    def set_browse_callback(self, callback: Callable):
        """Set callback for browse action."""
        self.browse_callback = callback

    def set_model_change_callback(self, callback: Callable):
        """Set callback for model change."""
        self.model_change_callback = callback

    def set_download_callback(self, callback: Callable):
        """Set callback for model download."""
        self.download_callback = callback

    def get_input_text(self) -> str:
        """Get text from input field."""
        return self.message_input.get("1.0", self.tk.END).strip()

    def clear_input(self):
        """Clear the input field."""
        self.message_input.delete("1.0", self.tk.END)

    def add_message(self, sender: str, message: str, tag: str = ""):
        """Add a message to the chat display with performance optimization."""
        # Batch GUI updates for better performance
        if not self._update_pending:
            self._update_pending = True
            self.root.after_idle(self._process_message_update, sender, message, tag)

    def _process_message_update(self, sender: str, message: str, tag: str):
        """Process message update in batch for better performance."""
        self.chat_display.config(state=self.tk.NORMAL)

        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Format and add message
        if sender:
            self.chat_display.insert(self.tk.END, f"[{timestamp}] {sender}: ", tag or sender.lower())

        self.chat_display.insert(self.tk.END, f"{message}\n\n", tag)

        self.chat_display.config(state=self.tk.DISABLED)
        self.auto_scroll()
        self._update_pending = False

    def show_typing_indicator(self):
        """Show typing indicator."""
        if not self.typing_active:
            self.typing_active = True
            self.chat_display.config(state=self.tk.NORMAL)
            self.chat_display.insert(self.tk.END, f"🤖 {self.current_model} is thinking...\n", "typing")
            self.chat_display.config(state=self.tk.DISABLED)
            self.auto_scroll()

    def hide_typing_indicator(self):
        """Hide typing indicator."""
        if self.typing_active:
            self.typing_active = False
            self.chat_display.config(state=self.tk.NORMAL)
            # Remove the typing indicator line
            self.chat_display.delete("end-2l", "end-1l")
            self.chat_display.config(state=self.tk.DISABLED)

    def auto_scroll(self):
        """Auto-scroll to bottom with performance optimization."""
        import time
        current_time = time.time()

        # Throttle scroll updates for better performance
        if current_time - self._last_scroll_time > self._scroll_delay:
            self.chat_display.see(self.tk.END)
            self._last_scroll_time = current_time

    def clear_chat(self):
        """Clear chat history."""
        if self.messagebox.askyesno("Clear Chat", "Clear all chat history?"):
            self.chat_display.config(state=self.tk.NORMAL)
            self.chat_display.delete("1.0", self.tk.END)
            self.chat_display.config(state=self.tk.DISABLED)

    def update_models(self, models: List[Dict]):
        """Update available models in the dropdown."""
        self.available_models = models
        model_names = [model['actual_name'] for model in models]
        self.model_combo['values'] = model_names

        # Set current model if available
        if self.current_model in model_names:
            self.model_var.set(self.current_model)
        elif model_names:
            self.current_model = model_names[0]
            self.model_var.set(self.current_model)

        self.update_model_info(self.current_model)

    def update_model_info(self, model_name: str):
        """Update model information display."""
        model_info = None
        for model in self.available_models:
            if model['actual_name'] == model_name:
                model_info = model
                break

        if model_info:
            info_text = f"{model_info['display_name']}\n\n{model_info['description']}"
            if 'size' in model_info:
                info_text += f"\n\nSize: {model_info['size']}"
        else:
            info_text = f"Model: {model_name}\n\nNo additional information available."

        self.model_info_label.config(text=info_text)

    def update_connection_status(self, message: str, is_connected: bool):
        """Update connection status."""
        color = COLORS['success_msg'] if is_connected else COLORS['error_msg']
        self.connection_label.config(text=message, foreground=color)

    def update_status(self, message: str):
        """Update status bar."""
        self.status_label.config(text=message)

    def set_input_enabled(self, enabled: bool):
        """Enable/disable input controls."""
        state = self.tk.NORMAL if enabled else self.tk.DISABLED
        self.message_input.config(state=state)
        self.send_button.config(state=state)
        self.browse_button.config(state=state)
        self.model_combo.config(state='readonly' if enabled else self.tk.DISABLED)
