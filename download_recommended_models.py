#!/usr/bin/env python3
"""Download recommended AI models for optimal task delegation."""

import subprocess
import sys
import time
from model_coordinator import get_recommended_models

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {str(e)}")
        return False

def check_ollama_installed():
    """Check if Ollama is installed."""
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ollama is installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama is not installed or not in PATH")
            return False
    except Exception:
        print("❌ Ollama is not installed or not in PATH")
        return False

def check_ollama_running():
    """Check if Ollama service is running."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            return True
        else:
            print("❌ Ollama service is not responding")
            return False
    except Exception:
        print("❌ Ollama service is not running")
        return False

def get_installed_models():
    """Get list of currently installed models."""
    try:
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    model_name = line.split()[0]
                    models.append(model_name)
            return models
        else:
            return []
    except Exception:
        return []

def download_model(model_name):
    """Download a specific model."""
    print(f"\n📥 Downloading {model_name}...")
    print("This may take several minutes depending on model size and internet speed...")
    
    try:
        # Use subprocess.Popen for real-time output
        process = subprocess.Popen(
            f"ollama pull {model_name}",
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print output in real-time
        for line in process.stdout:
            print(line.strip())
        
        process.wait()
        
        if process.returncode == 0:
            print(f"✅ Successfully downloaded {model_name}")
            return True
        else:
            print(f"❌ Failed to download {model_name}")
            return False
            
    except Exception as e:
        print(f"❌ Error downloading {model_name}: {str(e)}")
        return False

def main():
    """Main function to download recommended models."""
    print("🤖 AI Model Downloader for Enhanced Task Delegation")
    print("=" * 60)
    
    # Check prerequisites
    if not check_ollama_installed():
        print("\n❌ Please install Ollama first:")
        print("   Visit: https://ollama.ai/download")
        return
    
    if not check_ollama_running():
        print("\n❌ Please start Ollama service:")
        print("   Run: ollama serve")
        return
    
    # Get recommended models
    recommended_models = get_recommended_models()
    print(f"\n📋 Recommended models for optimal task delegation:")
    for i, model in enumerate(recommended_models, 1):
        print(f"   {i}. {model}")
    
    # Get currently installed models
    installed_models = get_installed_models()
    print(f"\n📦 Currently installed models: {len(installed_models)}")
    for model in installed_models:
        print(f"   ✅ {model}")
    
    # Determine which models to download
    models_to_download = []
    for model in recommended_models:
        if not any(installed.startswith(model.split(':')[0]) for installed in installed_models):
            models_to_download.append(model)
    
    if not models_to_download:
        print("\n🎉 All recommended models are already installed!")
        print("Your AI assistant is ready for intelligent task delegation!")
        return
    
    print(f"\n📥 Models to download: {len(models_to_download)}")
    for model in models_to_download:
        print(f"   📦 {model}")
    
    # Ask for confirmation
    print(f"\nThis will download {len(models_to_download)} models.")
    print("Note: Each model is typically 2-7GB in size.")
    
    response = input("\nProceed with download? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Download cancelled.")
        return
    
    # Download models
    successful_downloads = 0
    failed_downloads = 0
    
    for model in models_to_download:
        success = download_model(model)
        if success:
            successful_downloads += 1
        else:
            failed_downloads += 1
        
        # Small delay between downloads
        if model != models_to_download[-1]:
            time.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Download Summary:")
    print(f"✅ Successful: {successful_downloads}")
    print(f"❌ Failed: {failed_downloads}")
    print(f"📦 Total models now available: {len(installed_models) + successful_downloads}")
    
    if successful_downloads > 0:
        print("\n🎉 Model downloads completed!")
        print("🚀 Your AI assistant now has enhanced capabilities:")
        print("   • Intelligent task delegation")
        print("   • Specialized models for different tasks")
        print("   • Better code generation")
        print("   • Improved creative writing")
        print("   • Faster responses for simple tasks")
        
        print("\n🎯 What you can do now:")
        print("   • Ask for coding help (uses CodeLlama)")
        print("   • Request creative writing (uses Llama2)")
        print("   • Get quick answers (uses Phi3 or Gemma)")
        print("   • Complex analysis (uses larger models)")
        
        print("\n🔧 Next steps:")
        print("   1. Run: python enhanced_main.py")
        print("   2. Try: 'Write me a poem about AI'")
        print("   3. Try: 'Create a Python calculator'")
        print("   4. Try: 'Open YouTube and search for games'")
    
    if failed_downloads > 0:
        print(f"\n⚠️ {failed_downloads} models failed to download.")
        print("You can try downloading them manually:")
        for model in models_to_download:
            print(f"   ollama pull {model}")

if __name__ == "__main__":
    main()
