#!/usr/bin/env python3
"""Autonomous agent for executing complex tasks with real-time decision making."""

import time
import threading
import json
from typing import List, Dict, Optional, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from voice_interface import voice_interface
from screen_automation import screen_automation
from multimodal_handler import multimodal_handler


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ActionType(Enum):
    """Types of actions the agent can perform."""
    CLICK = "click"
    TYPE = "type"
    KEY_PRESS = "key_press"
    OPEN_APP = "open_app"
    WAIT = "wait"
    SCREENSHOT = "screenshot"
    ANALYZE_SCREEN = "analyze_screen"
    SPEAK = "speak"
    LISTEN = "listen"
    CUSTOM = "custom"


@dataclass
class Action:
    """Individual action in a task."""
    type: ActionType
    parameters: Dict[str, Any]
    description: str = ""
    timeout: float = 30.0
    retry_count: int = 3
    confirmation_required: bool = False


@dataclass
class Task:
    """A task consisting of multiple actions."""
    id: str
    name: str
    description: str
    actions: List[Action]
    status: TaskStatus = TaskStatus.PENDING
    current_action_index: int = 0
    created_at: float = 0.0
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class AgentSettings:
    """Autonomous agent configuration."""
    auto_confirm_safe_actions: bool = True
    max_retry_attempts: int = 3
    action_delay: float = 0.5
    screenshot_on_error: bool = True
    voice_feedback: bool = True
    continuous_monitoring: bool = True
    safety_mode: bool = True


class AutonomousAgent:
    """Autonomous agent for executing complex tasks."""
    
    def __init__(self, settings: AgentSettings = None):
        self.settings = settings or AgentSettings()
        self.current_task: Optional[Task] = None
        self.task_queue: List[Task] = []
        self.execution_active = False
        self.execution_thread: Optional[threading.Thread] = None
        self.stop_execution = threading.Event()
        
        # Callbacks
        self.on_task_started: Optional[Callable[[Task], None]] = None
        self.on_task_completed: Optional[Callable[[Task], None]] = None
        self.on_task_failed: Optional[Callable[[Task, str], None]] = None
        self.on_action_started: Optional[Callable[[Action], None]] = None
        self.on_action_completed: Optional[Callable[[Action], None]] = None
        self.on_confirmation_required: Optional[Callable[[Action], bool]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        
        # Task templates
        self.task_templates = self._load_task_templates()
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize voice and screen automation components."""
        try:
            # Set up voice interface callbacks
            voice_interface.on_speech_recognized = self._handle_voice_command
            voice_interface.on_error = self._handle_voice_error
            
            # Set up screen automation callbacks
            screen_automation.on_error = self._handle_screen_error
            
        except Exception as e:
            self._handle_error(f"Failed to initialize components: {str(e)}")
    
    def _load_task_templates(self) -> Dict[str, List[Action]]:
        """Load predefined task templates."""
        return {
            "open_pycharm_and_create_simulation": [
                Action(
                    type=ActionType.SPEAK,
                    parameters={"text": "Opening PyCharm and creating a simulation"},
                    description="Announce task start"
                ),
                Action(
                    type=ActionType.OPEN_APP,
                    parameters={"app_name": "pycharm"},
                    description="Open PyCharm IDE",
                    timeout=15.0
                ),
                Action(
                    type=ActionType.WAIT,
                    parameters={"duration": 3.0},
                    description="Wait for PyCharm to load"
                ),
                Action(
                    type=ActionType.KEY_PRESS,
                    parameters={"key": "ctrl+n"},
                    description="Create new file"
                ),
                Action(
                    type=ActionType.WAIT,
                    parameters={"duration": 1.0},
                    description="Wait for new file dialog"
                ),
                Action(
                    type=ActionType.TYPE,
                    parameters={"text": "simulation.py"},
                    description="Enter filename"
                ),
                Action(
                    type=ActionType.KEY_PRESS,
                    parameters={"key": "enter"},
                    description="Confirm filename"
                ),
                Action(
                    type=ActionType.WAIT,
                    parameters={"duration": 2.0},
                    description="Wait for file to open"
                ),
                Action(
                    type=ActionType.TYPE,
                    parameters={"text": self._get_simulation_code()},
                    description="Type simulation code"
                ),
                Action(
                    type=ActionType.KEY_PRESS,
                    parameters={"key": "ctrl+s"},
                    description="Save file"
                ),
                Action(
                    type=ActionType.SPEAK,
                    parameters={"text": "Simulation created successfully! The code includes a simple particle physics simulation with visualization."},
                    description="Announce completion"
                )
            ],
            
            "take_screenshot_and_analyze": [
                Action(
                    type=ActionType.SPEAK,
                    parameters={"text": "Taking screenshot and analyzing the screen"},
                    description="Announce action"
                ),
                Action(
                    type=ActionType.SCREENSHOT,
                    parameters={"save_path": "current_screen.png"},
                    description="Capture current screen"
                ),
                Action(
                    type=ActionType.ANALYZE_SCREEN,
                    parameters={"image_path": "current_screen.png"},
                    description="Analyze screenshot content"
                ),
                Action(
                    type=ActionType.SPEAK,
                    parameters={"text": "Screen analysis completed"},
                    description="Announce completion"
                )
            ]
        }
    
    def _get_simulation_code(self) -> str:
        """Get sample simulation code."""
        return '''#!/usr/bin/env python3
"""Simple particle physics simulation with visualization."""

import pygame
import math
import random

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
PARTICLE_COUNT = 50
GRAVITY = 0.1
FRICTION = 0.99

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)

class Particle:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.vx = random.uniform(-2, 2)
        self.vy = random.uniform(-2, 2)
        self.radius = random.randint(3, 8)
        self.color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
        self.mass = self.radius * 0.1
    
    def update(self):
        # Apply gravity
        self.vy += GRAVITY
        
        # Apply friction
        self.vx *= FRICTION
        self.vy *= FRICTION
        
        # Update position
        self.x += self.vx
        self.y += self.vy
        
        # Bounce off walls
        if self.x - self.radius <= 0 or self.x + self.radius >= SCREEN_WIDTH:
            self.vx *= -0.8
            self.x = max(self.radius, min(SCREEN_WIDTH - self.radius, self.x))
        
        if self.y - self.radius <= 0 or self.y + self.radius >= SCREEN_HEIGHT:
            self.vy *= -0.8
            self.y = max(self.radius, min(SCREEN_HEIGHT - self.radius, self.y))
    
    def draw(self, screen):
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.radius)

def main():
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Particle Physics Simulation")
    clock = pygame.time.Clock()
    
    # Create particles
    particles = []
    for _ in range(PARTICLE_COUNT):
        x = random.randint(50, SCREEN_WIDTH - 50)
        y = random.randint(50, SCREEN_HEIGHT - 50)
        particles.append(Particle(x, y))
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                # Add particle at mouse position
                mouse_x, mouse_y = pygame.mouse.get_pos()
                particles.append(Particle(mouse_x, mouse_y))
        
        # Update particles
        for particle in particles:
            particle.update()
        
        # Draw everything
        screen.fill(BLACK)
        for particle in particles:
            particle.draw(screen)
        
        # Display info
        font = pygame.font.Font(None, 36)
        text = font.render(f"Particles: {len(particles)}", True, WHITE)
        screen.blit(text, (10, 10))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

if __name__ == "__main__":
    main()
'''
    
    def create_task_from_template(self, template_name: str, task_name: str = None) -> Optional[Task]:
        """Create a task from a predefined template."""
        if template_name not in self.task_templates:
            self._handle_error(f"Unknown task template: {template_name}")
            return None
        
        task_id = f"task_{int(time.time())}"
        task_name = task_name or template_name.replace("_", " ").title()
        
        task = Task(
            id=task_id,
            name=task_name,
            description=f"Automated task: {task_name}",
            actions=self.task_templates[template_name].copy(),
            created_at=time.time()
        )
        
        return task
    
    def create_custom_task(self, name: str, description: str, actions: List[Action]) -> Task:
        """Create a custom task."""
        task_id = f"task_{int(time.time())}"
        
        return Task(
            id=task_id,
            name=name,
            description=description,
            actions=actions,
            created_at=time.time()
        )
    
    def add_task_to_queue(self, task: Task):
        """Add task to execution queue."""
        self.task_queue.append(task)
        
        if self.settings.voice_feedback:
            voice_interface.speak(f"Task '{task.name}' added to queue")
    
    def execute_task(self, task: Task) -> bool:
        """Execute a single task."""
        if self.execution_active:
            self._handle_error("Another task is already executing")
            return False
        
        self.current_task = task
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = time.time()
        
        if self.on_task_started:
            self.on_task_started(task)
        
        if self.settings.voice_feedback:
            voice_interface.speak(f"Starting task: {task.name}")
        
        self.execution_active = True
        self.stop_execution.clear()
        
        self.execution_thread = threading.Thread(target=self._execute_task_thread, args=(task,), daemon=True)
        self.execution_thread.start()
        
        return True
    
    def _execute_task_thread(self, task: Task):
        """Execute task in separate thread."""
        try:
            for i, action in enumerate(task.actions):
                if self.stop_execution.is_set():
                    task.status = TaskStatus.CANCELLED
                    break
                
                task.current_action_index = i
                
                if self.on_action_started:
                    self.on_action_started(action)
                
                # Check if confirmation is required
                if action.confirmation_required and not self.settings.auto_confirm_safe_actions:
                    if self.on_confirmation_required:
                        if not self.on_confirmation_required(action):
                            task.status = TaskStatus.CANCELLED
                            break
                
                # Execute action with retries
                success = False
                for attempt in range(action.retry_count):
                    if self.stop_execution.is_set():
                        break
                    
                    success = self._execute_action(action)
                    if success:
                        break
                    
                    if attempt < action.retry_count - 1:
                        time.sleep(1.0)  # Wait before retry
                
                if not success:
                    task.status = TaskStatus.FAILED
                    task.error_message = f"Failed to execute action: {action.description}"
                    break
                
                if self.on_action_completed:
                    self.on_action_completed(action)
                
                # Delay between actions
                time.sleep(self.settings.action_delay)
            
            # Task completion
            if task.status == TaskStatus.IN_PROGRESS:
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                
                if self.on_task_completed:
                    self.on_task_completed(task)
                
                if self.settings.voice_feedback:
                    voice_interface.speak(f"Task '{task.name}' completed successfully")
            
            elif task.status == TaskStatus.FAILED:
                if self.on_task_failed:
                    self.on_task_failed(task, task.error_message or "Unknown error")
                
                if self.settings.voice_feedback:
                    voice_interface.speak(f"Task '{task.name}' failed")
        
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            self._handle_error(f"Task execution error: {str(e)}")
        
        finally:
            self.execution_active = False
            self.current_task = None
    
    def _execute_action(self, action: Action) -> bool:
        """Execute a single action."""
        try:
            if action.type == ActionType.CLICK:
                return screen_automation.click_at_position(
                    action.parameters.get('x', 0),
                    action.parameters.get('y', 0),
                    action.parameters.get('button', 'left')
                )
            
            elif action.type == ActionType.TYPE:
                return screen_automation.type_text(action.parameters.get('text', ''))
            
            elif action.type == ActionType.KEY_PRESS:
                return screen_automation.press_key(action.parameters.get('key', ''))
            
            elif action.type == ActionType.OPEN_APP:
                return screen_automation.open_application(
                    action.parameters.get('app_name', ''),
                    action.parameters.get('app_path')
                )
            
            elif action.type == ActionType.WAIT:
                time.sleep(action.parameters.get('duration', 1.0))
                return True
            
            elif action.type == ActionType.SCREENSHOT:
                screenshot = screen_automation.capture_screen()
                if screenshot is not None:
                    save_path = action.parameters.get('save_path', 'screenshot.png')
                    import cv2
                    cv2.imwrite(save_path, screenshot)
                    return True
                return False
            
            elif action.type == ActionType.ANALYZE_SCREEN:
                image_path = action.parameters.get('image_path', 'screenshot.png')
                success, analysis = multimodal_handler.analyze_image_with_ollama(image_path)
                if success and self.settings.voice_feedback:
                    voice_interface.speak(f"Screen analysis: {analysis}")
                return success
            
            elif action.type == ActionType.SPEAK:
                text = action.parameters.get('text', '')
                voice_interface.speak(text)
                return True
            
            elif action.type == ActionType.LISTEN:
                # Start listening for voice input
                return voice_interface.start_listening(continuous=False)
            
            else:
                self._handle_error(f"Unknown action type: {action.type}")
                return False
        
        except Exception as e:
            self._handle_error(f"Action execution error: {str(e)}")
            return False
    
    def stop_current_task(self):
        """Stop the currently executing task."""
        self.stop_execution.set()
        if self.current_task:
            self.current_task.status = TaskStatus.CANCELLED
    
    def _handle_voice_command(self, command: str):
        """Handle voice commands during task execution."""
        command = command.lower().strip()
        
        if "stop" in command or "cancel" in command:
            self.stop_current_task()
            voice_interface.speak("Task cancelled")
        elif "pause" in command:
            # Implement pause functionality if needed
            voice_interface.speak("Pausing task")
        elif "continue" in command:
            voice_interface.speak("Continuing task")
    
    def _handle_voice_error(self, error: str):
        """Handle voice interface errors."""
        self._handle_error(f"Voice error: {error}")
    
    def _handle_screen_error(self, error: str):
        """Handle screen automation errors."""
        self._handle_error(f"Screen automation error: {error}")
    
    def _handle_error(self, error_message: str):
        """Handle errors."""
        if self.on_error:
            self.on_error(error_message)
        else:
            print(f"Autonomous Agent Error: {error_message}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            'execution_active': self.execution_active,
            'current_task': asdict(self.current_task) if self.current_task else None,
            'queue_length': len(self.task_queue),
            'available_templates': list(self.task_templates.keys())
        }


# Global autonomous agent instance
autonomous_agent = AutonomousAgent()

def execute_template_task(template_name: str, task_name: str = None) -> bool:
    """Execute a task from template."""
    task = autonomous_agent.create_task_from_template(template_name, task_name)
    if task:
        autonomous_agent.add_task_to_queue(task)
        return autonomous_agent.execute_task(task)
    return False

def create_and_execute_custom_task(name: str, description: str, actions: List[Action]) -> bool:
    """Create and execute a custom task."""
    task = autonomous_agent.create_custom_task(name, description, actions)
    autonomous_agent.add_task_to_queue(task)
    return autonomous_agent.execute_task(task)
