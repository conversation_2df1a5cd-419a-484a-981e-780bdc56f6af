#!/usr/bin/env python3
"""Install voice and autonomous capabilities dependencies."""

import subprocess
import sys
import platform
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {str(e)}")
        return False

def install_pip_packages():
    """Install Python packages via pip."""
    packages = [
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90", 
        "pyaudio>=0.2.11",
        "pydub>=0.25.1",
        "pyautogui>=0.9.54",
        "opencv-python>=4.8.0",
        "numpy>=1.24.0",
        "sounddevice>=0.4.6",
        "Pillow>=9.0.0"
    ]
    
    print("📦 Installing Python packages...")
    
    for package in packages:
        success = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}, continuing with others...")
    
    return True

def install_system_dependencies():
    """Install system-level dependencies."""
    system = platform.system().lower()
    
    if system == "windows":
        print("🪟 Windows detected")
        print("ℹ️ For Windows, most dependencies should install via pip.")
        print("ℹ️ If PyAudio fails, you may need to install it from:")
        print("   https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
        
    elif system == "linux":
        print("🐧 Linux detected")
        
        # Try to detect package manager
        if os.path.exists("/usr/bin/apt"):
            print("📦 Using apt package manager...")
            commands = [
                "sudo apt update",
                "sudo apt install -y python3-dev",
                "sudo apt install -y portaudio19-dev",
                "sudo apt install -y espeak espeak-data",
                "sudo apt install -y python3-tk",
                "sudo apt install -y scrot"  # For screenshots
            ]
            
            for cmd in commands:
                run_command(cmd, f"Running: {cmd}")
                
        elif os.path.exists("/usr/bin/yum"):
            print("📦 Using yum package manager...")
            commands = [
                "sudo yum install -y python3-devel",
                "sudo yum install -y portaudio-devel",
                "sudo yum install -y espeak",
                "sudo yum install -y tkinter",
                "sudo yum install -y scrot"
            ]
            
            for cmd in commands:
                run_command(cmd, f"Running: {cmd}")
        else:
            print("⚠️ Unknown Linux package manager. Please install manually:")
            print("   - python3-dev/python3-devel")
            print("   - portaudio19-dev/portaudio-devel") 
            print("   - espeak")
            print("   - tkinter")
            
    elif system == "darwin":
        print("🍎 macOS detected")
        
        if os.path.exists("/opt/homebrew/bin/brew") or os.path.exists("/usr/local/bin/brew"):
            print("🍺 Using Homebrew...")
            commands = [
                "brew install portaudio",
                "brew install espeak",
                "brew install python-tk"
            ]
            
            for cmd in commands:
                run_command(cmd, f"Running: {cmd}")
        else:
            print("⚠️ Homebrew not found. Please install manually:")
            print("   - portaudio")
            print("   - espeak")
            print("   - python-tk")
    
    return True

def test_installations():
    """Test if installations were successful."""
    print("\n🧪 Testing installations...")
    
    tests = [
        ("speech_recognition", "Speech Recognition"),
        ("pyttsx3", "Text-to-Speech"),
        ("pyautogui", "Screen Automation"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow")
    ]
    
    results = {}
    
    for module, name in tests:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
            results[name] = True
        except ImportError as e:
            print(f"❌ {name} - Failed: {str(e)}")
            results[name] = False
    
    # Special test for PyAudio
    try:
        import pyaudio
        print("✅ PyAudio - OK")
        results["PyAudio"] = True
    except ImportError as e:
        print(f"❌ PyAudio - Failed: {str(e)}")
        print("ℹ️ PyAudio installation tips:")
        if platform.system().lower() == "windows":
            print("   Windows: Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
        elif platform.system().lower() == "linux":
            print("   Linux: sudo apt install portaudio19-dev (Ubuntu/Debian)")
        elif platform.system().lower() == "darwin":
            print("   macOS: brew install portaudio")
        results["PyAudio"] = False
    
    return results

def main():
    """Main installation function."""
    print("🎤🤖 AI Assistant Voice & Autonomous Capabilities Installer")
    print("=" * 60)
    
    print(f"🖥️ Operating System: {platform.system()} {platform.release()}")
    print(f"🐍 Python Version: {sys.version}")
    
    # Install system dependencies
    install_system_dependencies()
    
    # Install Python packages
    install_pip_packages()
    
    # Test installations
    results = test_installations()
    
    print("\n📊 Installation Summary:")
    print("=" * 30)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    for component, success in results.items():
        status = "✅ OK" if success else "❌ FAILED"
        print(f"{component:20} {status}")
    
    print(f"\n🎯 Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("\n🎉 All dependencies installed successfully!")
        print("🚀 You can now use voice and autonomous features!")
    else:
        print(f"\n⚠️ {total_count - success_count} dependencies failed to install.")
        print("📖 Please check the error messages above and install manually if needed.")
    
    print("\n🔧 Next Steps:")
    print("1. Run the AI assistant: python enhanced_main.py")
    print("2. Click the '🎤 Voice' button to enable voice mode")
    print("3. Click the '🤖 Auto' button to run autonomous tasks")
    print("4. Say 'Hey Assistant' to activate voice commands")

if __name__ == "__main__":
    main()
