#!/usr/bin/env python3
"""Model benchmarking system to test all available models for optimal delegation."""

import time
import json
import statistics
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import queue

from enhanced_ollama_api import EnhancedOllamaAPI

@dataclass
class BenchmarkResult:
    """Results from benchmarking a model."""
    model_name: str
    task_type: str
    prompt: str
    response: str
    response_time: float
    response_length: int
    quality_score: float
    accuracy_score: float
    creativity_score: float
    coherence_score: float
    timestamp: str

@dataclass
class ModelPerformance:
    """Overall performance metrics for a model."""
    model_name: str
    avg_response_time: float
    avg_quality_score: float
    avg_accuracy_score: float
    avg_creativity_score: float
    avg_coherence_score: float
    best_task_types: List[str]
    total_tests: int
    last_updated: str

class ModelBenchmarker:
    """Comprehensive model testing and benchmarking system."""
    
    def __init__(self):
        self.ollama = EnhancedOllamaAPI()
        self.benchmark_results = []
        self.model_performances = {}
        self.test_prompts = self._initialize_test_prompts()
        
    def _initialize_test_prompts(self) -> Dict[str, List[Dict[str, str]]]:
        """Initialize comprehensive test prompts for different task types."""
        return {
            "coding": [
                {
                    "prompt": "Write a Python function to calculate the factorial of a number using recursion.",
                    "expected_keywords": ["def", "factorial", "return", "if", "else", "recursive"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Create a simple calculator class in Python with basic operations.",
                    "expected_keywords": ["class", "Calculator", "def", "add", "subtract", "multiply", "divide"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Write a function to reverse a string without using built-in reverse methods.",
                    "expected_keywords": ["def", "reverse", "string", "return", "loop"],
                    "complexity": "easy"
                },
                {
                    "prompt": "Implement a binary search algorithm in Python.",
                    "expected_keywords": ["def", "binary_search", "while", "mid", "left", "right"],
                    "complexity": "hard"
                }
            ],
            "creative_writing": [
                {
                    "prompt": "Write a short poem about artificial intelligence and the future.",
                    "expected_keywords": ["AI", "future", "technology", "digital", "intelligence"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Create a brief story about a robot learning to paint.",
                    "expected_keywords": ["robot", "paint", "art", "learn", "creative"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Write a haiku about programming.",
                    "expected_keywords": ["code", "program", "debug", "compile", "syntax"],
                    "complexity": "easy"
                },
                {
                    "prompt": "Compose a letter from the perspective of an AI to humanity.",
                    "expected_keywords": ["Dear", "humanity", "artificial", "intelligence", "future"],
                    "complexity": "hard"
                }
            ],
            "problem_solving": [
                {
                    "prompt": "Explain how to solve the Tower of Hanoi puzzle with 3 disks.",
                    "expected_keywords": ["Tower", "Hanoi", "disks", "move", "rules", "steps"],
                    "complexity": "medium"
                },
                {
                    "prompt": "How would you optimize a slow database query?",
                    "expected_keywords": ["database", "query", "index", "optimize", "performance"],
                    "complexity": "hard"
                },
                {
                    "prompt": "What's the best way to organize a large codebase?",
                    "expected_keywords": ["organize", "structure", "modules", "architecture", "maintainable"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Explain the difference between recursion and iteration.",
                    "expected_keywords": ["recursion", "iteration", "loop", "function", "call"],
                    "complexity": "easy"
                }
            ],
            "conversation": [
                {
                    "prompt": "What are the benefits and drawbacks of remote work?",
                    "expected_keywords": ["remote", "work", "benefits", "drawbacks", "productivity"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Explain quantum computing in simple terms.",
                    "expected_keywords": ["quantum", "computing", "qubits", "superposition", "simple"],
                    "complexity": "hard"
                },
                {
                    "prompt": "What's your favorite programming language and why?",
                    "expected_keywords": ["programming", "language", "favorite", "because", "features"],
                    "complexity": "easy"
                },
                {
                    "prompt": "How do you think AI will change education in the next 10 years?",
                    "expected_keywords": ["AI", "education", "change", "future", "learning"],
                    "complexity": "medium"
                }
            ],
            "analysis": [
                {
                    "prompt": "Compare the pros and cons of Python vs JavaScript for web development.",
                    "expected_keywords": ["Python", "JavaScript", "web", "development", "pros", "cons"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Analyze the time complexity of bubble sort algorithm.",
                    "expected_keywords": ["bubble", "sort", "time", "complexity", "O(n²)", "algorithm"],
                    "complexity": "hard"
                },
                {
                    "prompt": "What are the key differences between SQL and NoSQL databases?",
                    "expected_keywords": ["SQL", "NoSQL", "database", "differences", "structured"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Evaluate the security implications of cloud computing.",
                    "expected_keywords": ["security", "cloud", "computing", "risks", "benefits"],
                    "complexity": "hard"
                }
            ]
        }
    
    def _score_response(self, response: str, expected_keywords: List[str], task_type: str, complexity: str) -> Tuple[float, float, float, float]:
        """Score a response based on quality, accuracy, creativity, and coherence."""
        response_lower = response.lower()
        
        # Accuracy score based on keyword presence
        keyword_matches = sum(1 for keyword in expected_keywords if keyword.lower() in response_lower)
        accuracy_score = min(10.0, (keyword_matches / len(expected_keywords)) * 10)
        
        # Quality score based on length and structure
        quality_score = 0.0
        if len(response) > 50:
            quality_score += 3.0
        if len(response) > 200:
            quality_score += 3.0
        if len(response) > 500:
            quality_score += 2.0
        if '\n' in response:
            quality_score += 1.0
        if any(char in response for char in '.,!?'):
            quality_score += 1.0
        
        # Creativity score (higher for creative tasks)
        creativity_score = 5.0  # base score
        if task_type == "creative_writing":
            if any(word in response_lower for word in ['imagine', 'dream', 'beautiful', 'wonder', 'magic']):
                creativity_score += 2.0
            if len(set(response.split())) / len(response.split()) > 0.7:  # vocabulary diversity
                creativity_score += 2.0
            if any(char in response for char in '"\''):  # dialogue or quotes
                creativity_score += 1.0
        elif task_type == "coding":
            if 'def ' in response or 'class ' in response:
                creativity_score += 2.0
            if 'import ' in response:
                creativity_score += 1.0
            if '# ' in response or '"""' in response:  # comments
                creativity_score += 2.0
        
        # Coherence score based on structure
        coherence_score = 5.0  # base score
        sentences = response.split('.')
        if len(sentences) > 1:
            coherence_score += 2.0
        if response.strip().endswith(('.', '!', '?')):
            coherence_score += 1.0
        if not any(word in response_lower for word in ['error', 'sorry', 'cannot', "can't"]):
            coherence_score += 2.0
        
        return quality_score, accuracy_score, creativity_score, coherence_score
    
    def benchmark_model(self, model_name: str, max_tests_per_type: int = 2) -> List[BenchmarkResult]:
        """Benchmark a single model across all task types."""
        print(f"\n🧪 Testing model: {model_name}")
        results = []
        
        for task_type, prompts in self.test_prompts.items():
            print(f"  📝 Testing {task_type}...")
            
            for i, test_case in enumerate(prompts[:max_tests_per_type]):
                try:
                    # Switch to the model
                    success, message = self.ollama.set_model(model_name)
                    if not success:
                        print(f"    ❌ Failed to switch to {model_name}: {message}")
                        continue
                    
                    # Time the response
                    start_time = time.time()
                    success, response = self.ollama.generate_sync(test_case["prompt"])
                    end_time = time.time()
                    
                    if not success or not response:
                        print(f"    ❌ Failed to get response for {task_type} test {i+1}")
                        continue
                    
                    response_time = end_time - start_time
                    
                    # Score the response
                    quality_score, accuracy_score, creativity_score, coherence_score = self._score_response(
                        response, test_case["expected_keywords"], task_type, test_case["complexity"]
                    )
                    
                    result = BenchmarkResult(
                        model_name=model_name,
                        task_type=task_type,
                        prompt=test_case["prompt"],
                        response=response[:200] + "..." if len(response) > 200 else response,
                        response_time=response_time,
                        response_length=len(response),
                        quality_score=quality_score,
                        accuracy_score=accuracy_score,
                        creativity_score=creativity_score,
                        coherence_score=coherence_score,
                        timestamp=datetime.now().isoformat()
                    )
                    
                    results.append(result)
                    print(f"    ✅ {task_type} test {i+1}: {response_time:.2f}s, Q:{quality_score:.1f}, A:{accuracy_score:.1f}")
                    
                except Exception as e:
                    print(f"    ❌ Error testing {model_name} on {task_type}: {str(e)}")
                    continue
        
        return results
    
    def benchmark_all_models(self, max_tests_per_type: int = 2) -> Dict[str, ModelPerformance]:
        """Benchmark all available models."""
        print("🚀 Starting comprehensive model benchmarking...")
        
        # Get available models
        success, message = self.ollama.test_connection()
        if not success:
            print(f"❌ Cannot connect to Ollama: {message}")
            return {}
        
        available_models = self.ollama.get_available_models()
        if not available_models:
            print("❌ No models found. Make sure Ollama is running and models are installed.")
            return {}

        print(f"📋 Found {len(available_models)} models to test:")
        for i, model in enumerate(available_models, 1):
            print(f"   {i:2d}. {model}")
        
        all_results = []
        model_performances = {}
        
        for i, model_name in enumerate(available_models, 1):
            print(f"\n[{i}/{len(available_models)}] Testing {model_name}")
            
            try:
                model_results = self.benchmark_model(model_name, max_tests_per_type)
                all_results.extend(model_results)
                
                if model_results:
                    # Calculate performance metrics
                    avg_response_time = statistics.mean([r.response_time for r in model_results])
                    avg_quality_score = statistics.mean([r.quality_score for r in model_results])
                    avg_accuracy_score = statistics.mean([r.accuracy_score for r in model_results])
                    avg_creativity_score = statistics.mean([r.creativity_score for r in model_results])
                    avg_coherence_score = statistics.mean([r.coherence_score for r in model_results])
                    
                    # Find best task types for this model
                    task_scores = {}
                    for result in model_results:
                        if result.task_type not in task_scores:
                            task_scores[result.task_type] = []
                        overall_score = (result.quality_score + result.accuracy_score + result.creativity_score + result.coherence_score) / 4
                        task_scores[result.task_type].append(overall_score)
                    
                    # Average scores by task type
                    task_averages = {task: statistics.mean(scores) for task, scores in task_scores.items()}
                    best_task_types = sorted(task_averages.keys(), key=lambda x: task_averages[x], reverse=True)
                    
                    performance = ModelPerformance(
                        model_name=model_name,
                        avg_response_time=avg_response_time,
                        avg_quality_score=avg_quality_score,
                        avg_accuracy_score=avg_accuracy_score,
                        avg_creativity_score=avg_creativity_score,
                        avg_coherence_score=avg_coherence_score,
                        best_task_types=best_task_types,
                        total_tests=len(model_results),
                        last_updated=datetime.now().isoformat()
                    )
                    
                    model_performances[model_name] = performance
                    
                    print(f"  📊 Performance: Speed:{avg_response_time:.2f}s, Quality:{avg_quality_score:.1f}, Accuracy:{avg_accuracy_score:.1f}")
                    print(f"  🎯 Best at: {', '.join(best_task_types[:3])}")
                
            except Exception as e:
                print(f"  ❌ Failed to benchmark {model_name}: {str(e)}")
                continue
        
        # Save results
        self.benchmark_results = all_results
        self.model_performances = model_performances
        self.save_benchmark_results()
        
        return model_performances
    
    def save_benchmark_results(self):
        """Save benchmark results to files."""
        try:
            # Save detailed results
            with open("model_benchmark_results.json", "w") as f:
                json.dump([asdict(result) for result in self.benchmark_results], f, indent=2)
            
            # Save performance summary
            with open("model_performances.json", "w") as f:
                json.dump({name: asdict(perf) for name, perf in self.model_performances.items()}, f, indent=2)
            
            print(f"\n💾 Saved benchmark results to model_benchmark_results.json")
            print(f"💾 Saved performance summary to model_performances.json")
            
        except Exception as e:
            print(f"❌ Failed to save results: {str(e)}")
    
    def load_benchmark_results(self) -> bool:
        """Load existing benchmark results."""
        try:
            with open("model_performances.json", "r") as f:
                data = json.load(f)
                self.model_performances = {
                    name: ModelPerformance(**perf_data) 
                    for name, perf_data in data.items()
                }
            print(f"✅ Loaded benchmark results for {len(self.model_performances)} models")
            return True
        except FileNotFoundError:
            print("ℹ️ No existing benchmark results found")
            return False
        except Exception as e:
            print(f"❌ Failed to load benchmark results: {str(e)}")
            return False
    
    def get_best_model_for_task(self, task_type: str, prioritize_speed: bool = False) -> str:
        """Get the best model for a specific task type."""
        if not self.model_performances:
            return "phi3:mini"  # fallback
        
        candidates = []
        for model_name, performance in self.model_performances.items():
            if task_type in performance.best_task_types:
                # Calculate overall score
                if prioritize_speed:
                    # Prioritize speed (lower response time is better)
                    speed_score = max(0, 10 - performance.avg_response_time)
                    overall_score = (speed_score * 0.4 + 
                                   performance.avg_quality_score * 0.2 + 
                                   performance.avg_accuracy_score * 0.4)
                else:
                    # Prioritize quality and accuracy
                    overall_score = (performance.avg_quality_score * 0.3 + 
                                   performance.avg_accuracy_score * 0.4 + 
                                   performance.avg_creativity_score * 0.2 + 
                                   performance.avg_coherence_score * 0.1)
                
                candidates.append((model_name, overall_score, performance.avg_response_time))
        
        if candidates:
            # Sort by overall score, then by speed
            candidates.sort(key=lambda x: (x[1], -x[2]), reverse=True)
            return candidates[0][0]
        
        return "phi3:mini"  # fallback
    
    def print_performance_summary(self):
        """Print a summary of model performances."""
        if not self.model_performances:
            print("No benchmark results available")
            return
        
        print("\n" + "="*80)
        print("🏆 MODEL PERFORMANCE SUMMARY")
        print("="*80)
        
        # Sort models by overall performance
        models_by_performance = sorted(
            self.model_performances.items(),
            key=lambda x: (x[1].avg_quality_score + x[1].avg_accuracy_score) / 2,
            reverse=True
        )
        
        for i, (model_name, perf) in enumerate(models_by_performance, 1):
            print(f"\n{i:2d}. {model_name}")
            print(f"    ⚡ Speed: {perf.avg_response_time:.2f}s")
            print(f"    🎯 Quality: {perf.avg_quality_score:.1f}/10")
            print(f"    ✅ Accuracy: {perf.avg_accuracy_score:.1f}/10")
            print(f"    🎨 Creativity: {perf.avg_creativity_score:.1f}/10")
            print(f"    🧠 Coherence: {perf.avg_coherence_score:.1f}/10")
            print(f"    🏅 Best at: {', '.join(perf.best_task_types[:3])}")
        
        print("\n" + "="*80)
        print("🎯 TASK TYPE RECOMMENDATIONS")
        print("="*80)
        
        for task_type in ["coding", "creative_writing", "problem_solving", "conversation", "analysis"]:
            best_model = self.get_best_model_for_task(task_type)
            fast_model = self.get_best_model_for_task(task_type, prioritize_speed=True)
            print(f"{task_type.upper():20} → Best: {best_model:20} | Fastest: {fast_model}")

# Global benchmarker instance
model_benchmarker = ModelBenchmarker()

def run_full_benchmark():
    """Run a complete benchmark of all models."""
    return model_benchmarker.benchmark_all_models()

def get_best_model_for_task(task_type: str, prioritize_speed: bool = False) -> str:
    """Get the best model for a task type."""
    return model_benchmarker.get_best_model_for_task(task_type, prioritize_speed)
