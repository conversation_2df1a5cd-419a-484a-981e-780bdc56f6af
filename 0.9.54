Collecting pyautogui
  Using cached pyautogui-0.9.54-py3-none-any.whl
Collecting pymsgbox
  Using cached pymsgbox-1.0.9-py3-none-any.whl
Collecting mouseinfo
  Using cached MouseInfo-0.1.3-py3-none-any.whl
Collecting pyscreeze>=0.1.21
  Using cached pyscreeze-1.0.1-py3-none-any.whl
Collecting pytweening>=1.0.4
  Using cached pytweening-1.2.0-py3-none-any.whl
Collecting pygetwindow>=0.0.5
  Using cached PyGetWindow-0.0.9-py3-none-any.whl
Collecting pyrect
  Using cached PyRect-0.2.0-py2.py3-none-any.whl
Requirement already satisfied: Pillow>=9.2.0 in c:\users\<USER>\lib\site-packages (from pyscreeze>=0.1.21->pyautogui) (10.0.0)
Collecting pyperclip
  Using cached pyperclip-1.9.0-py3-none-any.whl
Installing collected packages: pytweening, pyrect, pyperclip, pymsgbox, pyscreeze, pygetwindow, mouseinfo, pyautogui
Successfully installed mouseinfo-0.1.3 pyautogui-0.9.54 pygetwindow-0.0.9 pymsgbox-1.0.9 pyperclip-1.9.0 pyrect-0.2.0 pyscreeze-1.0.1 pytweening-1.2.0
