#!/usr/bin/env python3
"""Intelligent model coordination system for task delegation."""

import json
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    """Types of tasks that can be delegated."""
    CODING = "coding"
    CREATIVE_WRITING = "creative_writing"
    WEB_SEARCH = "web_search"
    SYSTEM_AUTOMATION = "system_automation"
    DATA_ANALYSIS = "data_analysis"
    CONVERSATION = "conversation"
    PROBLEM_SOLVING = "problem_solving"
    CONTENT_GENERATION = "content_generation"

@dataclass
class ModelCapability:
    """Model capabilities and specializations."""
    model_name: str
    strengths: List[TaskType]
    description: str
    best_for: List[str]
    context_window: int
    speed_rating: int  # 1-10, 10 being fastest
    quality_rating: int  # 1-10, 10 being highest quality

@dataclass
class TaskRequest:
    """A task request with context and requirements."""
    original_request: str
    task_type: TaskType
    complexity: int  # 1-10
    requires_creativity: bool
    requires_accuracy: bool
    requires_speed: bool
    context: Dict[str, Any]

class ModelCoordinator:
    """Intelligent model coordinator for task delegation."""
    
    def __init__(self):
        self.models = self._initialize_model_capabilities()
        self.task_history = []
        self.model_performance = {}
        
    def _initialize_model_capabilities(self) -> Dict[str, ModelCapability]:
        """Initialize model capabilities database."""
        return {
            # Coding specialists
            "codellama:7b": ModelCapability(
                model_name="codellama:7b",
                strengths=[TaskType.CODING, TaskType.PROBLEM_SOLVING],
                description="Specialized for code generation, debugging, and programming tasks",
                best_for=["Python", "JavaScript", "web development", "algorithms", "debugging"],
                context_window=4096,
                speed_rating=8,
                quality_rating=9
            ),
            "codellama:13b": ModelCapability(
                model_name="codellama:13b",
                strengths=[TaskType.CODING, TaskType.PROBLEM_SOLVING],
                description="Advanced coding model for complex programming tasks",
                best_for=["complex algorithms", "system design", "code architecture", "optimization"],
                context_window=4096,
                speed_rating=6,
                quality_rating=10
            ),
            "deepseek-coder:6.7b": ModelCapability(
                model_name="deepseek-coder:6.7b",
                strengths=[TaskType.CODING, TaskType.SYSTEM_AUTOMATION],
                description="Excellent for code generation and system automation",
                best_for=["automation scripts", "DevOps", "system administration", "API integration"],
                context_window=4096,
                speed_rating=7,
                quality_rating=8
            ),
            
            # Creative and writing specialists
            "llama2:7b": ModelCapability(
                model_name="llama2:7b",
                strengths=[TaskType.CREATIVE_WRITING, TaskType.CONVERSATION, TaskType.CONTENT_GENERATION],
                description="Great for creative writing, storytelling, and general conversation",
                best_for=["poems", "stories", "creative content", "casual conversation"],
                context_window=4096,
                speed_rating=8,
                quality_rating=7
            ),
            "llama2:13b": ModelCapability(
                model_name="llama2:13b",
                strengths=[TaskType.CREATIVE_WRITING, TaskType.CONVERSATION, TaskType.PROBLEM_SOLVING],
                description="Advanced model for complex creative and analytical tasks",
                best_for=["complex writing", "analysis", "reasoning", "detailed explanations"],
                context_window=4096,
                speed_rating=6,
                quality_rating=9
            ),
            "mistral:7b": ModelCapability(
                model_name="mistral:7b",
                strengths=[TaskType.CONVERSATION, TaskType.PROBLEM_SOLVING, TaskType.CONTENT_GENERATION],
                description="Balanced model for conversation and general tasks",
                best_for=["general conversation", "explanations", "summaries", "Q&A"],
                context_window=8192,
                speed_rating=9,
                quality_rating=8
            ),
            
            # Fast and efficient models
            "phi3:mini": ModelCapability(
                model_name="phi3:mini",
                strengths=[TaskType.CONVERSATION, TaskType.SYSTEM_AUTOMATION],
                description="Fast, efficient model for quick tasks and automation",
                best_for=["quick responses", "simple automation", "system commands"],
                context_window=2048,
                speed_rating=10,
                quality_rating=6
            ),
            "gemma:2b": ModelCapability(
                model_name="gemma:2b",
                strengths=[TaskType.CONVERSATION, TaskType.CONTENT_GENERATION],
                description="Lightweight model for fast responses",
                best_for=["quick answers", "simple content", "fast interaction"],
                context_window=2048,
                speed_rating=10,
                quality_rating=5
            ),
            
            # Specialized models
            "neural-chat:7b": ModelCapability(
                model_name="neural-chat:7b",
                strengths=[TaskType.CONVERSATION, TaskType.PROBLEM_SOLVING],
                description="Optimized for natural conversation and problem solving",
                best_for=["natural dialogue", "help and support", "explanations"],
                context_window=4096,
                speed_rating=8,
                quality_rating=8
            ),
            "orca-mini:3b": ModelCapability(
                model_name="orca-mini:3b",
                strengths=[TaskType.CONVERSATION, TaskType.DATA_ANALYSIS],
                description="Good for reasoning and data analysis tasks",
                best_for=["logical reasoning", "data interpretation", "analysis"],
                context_window=2048,
                speed_rating=9,
                quality_rating=7
            )
        }
    
    def analyze_task(self, user_request: str) -> TaskRequest:
        """Analyze user request and determine task type and requirements."""
        request_lower = user_request.lower()
        
        # Determine task type
        task_type = TaskType.CONVERSATION  # default
        complexity = 5  # default
        requires_creativity = False
        requires_accuracy = False
        requires_speed = False
        
        # Coding tasks
        if any(word in request_lower for word in [
            "code", "program", "script", "function", "class", "algorithm",
            "python", "javascript", "html", "css", "java", "c++", "sql",
            "debug", "fix", "optimize", "refactor", "api", "database"
        ]):
            task_type = TaskType.CODING
            complexity = 7
            requires_accuracy = True
            
        # Creative writing
        elif any(word in request_lower for word in [
            "poem", "story", "creative", "write", "compose", "letter",
            "essay", "article", "blog", "narrative", "fiction"
        ]):
            task_type = TaskType.CREATIVE_WRITING
            complexity = 6
            requires_creativity = True
            
        # Web search and browsing
        elif any(word in request_lower for word in [
            "search", "google", "youtube", "website", "browse", "find online",
            "look up", "internet", "web", "url"
        ]):
            task_type = TaskType.WEB_SEARCH
            complexity = 4
            requires_speed = True
            
        # System automation
        elif any(word in request_lower for word in [
            "open", "launch", "start", "run", "execute", "automation",
            "system", "command", "file", "folder", "screenshot"
        ]):
            task_type = TaskType.SYSTEM_AUTOMATION
            complexity = 5
            requires_speed = True
            
        # Data analysis
        elif any(word in request_lower for word in [
            "analyze", "data", "statistics", "chart", "graph", "calculate",
            "compare", "evaluate", "assess", "measure"
        ]):
            task_type = TaskType.DATA_ANALYSIS
            complexity = 7
            requires_accuracy = True
            
        # Content generation
        elif any(word in request_lower for word in [
            "generate", "create", "make", "build", "design", "plan",
            "list", "outline", "summary", "report"
        ]):
            task_type = TaskType.CONTENT_GENERATION
            complexity = 6
            requires_creativity = True
            
        # Problem solving
        elif any(word in request_lower for word in [
            "solve", "problem", "issue", "help", "how to", "explain",
            "understand", "learn", "teach", "guide"
        ]):
            task_type = TaskType.PROBLEM_SOLVING
            complexity = 6
            requires_accuracy = True
        
        return TaskRequest(
            original_request=user_request,
            task_type=task_type,
            complexity=complexity,
            requires_creativity=requires_creativity,
            requires_accuracy=requires_accuracy,
            requires_speed=requires_speed,
            context={}
        )
    
    def select_best_model(self, task: TaskRequest) -> str:
        """Select the best model for a given task."""
        # Filter models that can handle this task type
        suitable_models = []
        
        for model_name, capability in self.models.items():
            if task.task_type in capability.strengths:
                # Calculate suitability score
                score = 0
                
                # Base score for having the right strength
                score += 10
                
                # Bonus for quality if accuracy is required
                if task.requires_accuracy:
                    score += capability.quality_rating * 2
                
                # Bonus for speed if speed is required
                if task.requires_speed:
                    score += capability.speed_rating * 2
                
                # Bonus for creativity if creativity is required
                if task.requires_creativity and task.task_type == TaskType.CREATIVE_WRITING:
                    score += capability.quality_rating
                
                # Complexity matching
                if task.complexity <= 5:
                    # For simple tasks, prefer faster models
                    score += capability.speed_rating
                else:
                    # For complex tasks, prefer quality
                    score += capability.quality_rating
                
                # Check if task keywords match model's best_for
                task_lower = task.original_request.lower()
                for specialty in capability.best_for:
                    if specialty.lower() in task_lower:
                        score += 5
                
                suitable_models.append((model_name, score, capability))
        
        if not suitable_models:
            # Fallback to general conversation models
            return "mistral:7b"  # Good general purpose model
        
        # Sort by score and return the best
        suitable_models.sort(key=lambda x: x[1], reverse=True)
        return suitable_models[0][0]
    
    def generate_enhanced_prompt(self, task: TaskRequest, selected_model: str) -> str:
        """Generate an enhanced prompt for the selected model."""
        model_capability = self.models.get(selected_model)
        
        # Base prompt with task context
        prompt = f"Task: {task.original_request}\n\n"
        
        # Add model-specific instructions
        if task.task_type == TaskType.CODING:
            prompt += "You are an expert programmer. Provide clean, well-commented code with explanations. "
            prompt += "Include error handling and best practices. "
            
        elif task.task_type == TaskType.CREATIVE_WRITING:
            prompt += "You are a creative writer. Be imaginative, engaging, and expressive. "
            prompt += "Use vivid language and create compelling content. "
            
        elif task.task_type == TaskType.WEB_SEARCH:
            prompt += "You are helping with web navigation and search. Provide specific, actionable instructions. "
            prompt += "Be precise about URLs, search terms, and navigation steps. "
            
        elif task.task_type == TaskType.SYSTEM_AUTOMATION:
            prompt += "You are a system automation expert. Provide clear, step-by-step instructions. "
            prompt += "Be specific about applications, commands, and actions. "
            
        elif task.task_type == TaskType.CONTENT_GENERATION:
            prompt += "You are a content creator. Generate useful, well-structured content. "
            prompt += "Be comprehensive and organized. "
        
        # Add quality requirements
        if task.requires_accuracy:
            prompt += "Accuracy is critical - double-check your response. "
        if task.requires_creativity:
            prompt += "Be creative and original in your approach. "
        if task.requires_speed:
            prompt += "Provide a concise but complete response. "
        
        prompt += f"\n\nPlease respond to: {task.original_request}"
        
        return prompt
    
    def delegate_task(self, user_request: str) -> Tuple[str, str, TaskRequest]:
        """Main delegation method - analyze task and select best model."""
        # Analyze the task
        task = self.analyze_task(user_request)
        
        # Select the best model
        selected_model = self.select_best_model(task)
        
        # Generate enhanced prompt
        enhanced_prompt = self.generate_enhanced_prompt(task, selected_model)
        
        # Record for learning
        self.task_history.append({
            "request": user_request,
            "task_type": task.task_type.value,
            "selected_model": selected_model,
            "complexity": task.complexity
        })
        
        return selected_model, enhanced_prompt, task
    
    def get_model_recommendations(self) -> List[str]:
        """Get recommended models to download based on capabilities."""
        return [
            "codellama:7b",      # Essential for coding
            "llama2:7b",         # Great for creative writing
            "mistral:7b",        # Excellent general purpose
            "phi3:mini",         # Fast for automation
            "deepseek-coder:6.7b", # Advanced coding
            "neural-chat:7b",    # Natural conversation
            "gemma:2b"           # Lightweight and fast
        ]
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """Get statistics about task delegation."""
        if not self.task_history:
            return {"total_tasks": 0}
        
        task_types = {}
        model_usage = {}
        
        for task in self.task_history:
            task_type = task["task_type"]
            model = task["selected_model"]
            
            task_types[task_type] = task_types.get(task_type, 0) + 1
            model_usage[model] = model_usage.get(model, 0) + 1
        
        return {
            "total_tasks": len(self.task_history),
            "task_types": task_types,
            "model_usage": model_usage,
            "most_used_model": max(model_usage.items(), key=lambda x: x[1])[0] if model_usage else None
        }

# Global coordinator instance
model_coordinator = ModelCoordinator()

def delegate_task(user_request: str) -> Tuple[str, str, TaskRequest]:
    """Delegate a task to the best model."""
    return model_coordinator.delegate_task(user_request)

def get_recommended_models() -> List[str]:
    """Get list of recommended models to download."""
    return model_coordinator.get_model_recommendations()
