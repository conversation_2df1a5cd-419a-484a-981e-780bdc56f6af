#!/usr/bin/env python3
"""
🤖 AI Assistant Launcher
Simple launcher script for the consolidated AI Assistant application.
"""

import sys
import os

def main():
    """Launch the AI Assistant application."""
    print("🚀 Launching AI Assistant...")
    
    try:
        # Import and run the main application
        from ai_assistant_app import main as app_main
        return app_main()
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("\nMissing dependencies. Please install required packages:")
        print("pip install tkinter requests")
        return False
        
    except Exception as e:
        print(f"❌ Failed to launch AI Assistant: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
