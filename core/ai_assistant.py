#!/usr/bin/env python3
"""Consolidated AI Assistant core combining all functionality."""

import time
from typing import Dict, List, Optional, Callable, Tuple, Any

from enhanced_ollama_api import EnhancedOllamaAPI
from chat_session_manager import session_manager
from command_parser import parse_and_execute, is_pc_command, get_command_help
from file_operations import FileOperations
from code_extractor import CodeExtractor

from core.config import get_config
from core.model_manager import get_model_manager, TaskType
from core.autonomous_system import get_autonomous_system, TaskRequest, AutonomousMode

class AIAssistant:
    """Consolidated AI Assistant with all capabilities."""
    
    def __init__(self):
        # Core components
        self.ollama = EnhancedOllamaAPI()
        self.file_ops = FileOperations()
        self.code_extractor = CodeExtractor()
        
        # Configuration
        self.config = get_config()
        
        # Managers
        self.model_manager = get_model_manager()
        self.autonomous_system = get_autonomous_system()
        
        # Current context
        self.current_file_path = None
        self.current_file_content = None
        
        # Initialize with default session if none exists
        if not session_manager.get_current_session():
            session_manager.create_new_session("phi3:mini", "Default Chat")
        
        # Set up autonomous system callbacks
        self._setup_autonomous_callbacks()
    
    def _setup_autonomous_callbacks(self):
        """Set up autonomous system callbacks."""
        self.autonomous_system.on_voice_command = self._handle_autonomous_voice_command
        self.autonomous_system.on_task_started = self._handle_autonomous_task_started
        self.autonomous_system.on_task_completed = self._handle_autonomous_task_completed
        self.autonomous_system.on_task_failed = self._handle_autonomous_task_failed
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test connection to Ollama."""
        return self.ollama.test_connection()
    
    def get_available_models(self) -> List[str]:
        """Get available models."""
        return self.ollama.get_available_models()
    
    def set_model(self, model_name: str) -> Tuple[bool, str]:
        """Set the current model."""
        return self.ollama.set_model(model_name)
    
    def get_current_model(self) -> str:
        """Get the current model."""
        return self.ollama.get_current_model()
    
    def process_user_input(self, user_input: str, on_response_callback: Callable,
                          on_error_callback: Callable, use_streaming: bool = False):
        """Process user input with intelligent model delegation."""
        
        # Add user input to history
        self.add_to_history("user", user_input)
        
        # Check if this is a PC interaction command
        if is_pc_command(user_input):
            self._handle_pc_command(user_input, on_response_callback, on_error_callback)
            return
        
        # Check for help request
        if any(word in user_input.lower() for word in ['help', 'commands', 'what can you do']):
            if 'pc' in user_input.lower() or 'computer' in user_input.lower():
                help_text = get_command_help()
                self._handle_ai_response(help_text, user_input, on_response_callback)
                return
        
        # Check if this is an actionable request for autonomous execution
        if self._is_actionable_request(user_input):
            success = self._execute_autonomous_request(user_input)
            if success:
                # Autonomous task started, provide feedback
                response = "I'll take care of that for you! 🤖"
                self._handle_ai_response(response, user_input, on_response_callback)
                return
        
        # Use intelligent model delegation for conversation
        try:
            # Delegate to best model
            selected_model, task_type = self.model_manager.delegate_request(user_input)
            
            # Switch to selected model if different
            current_model = self.ollama.get_current_model()
            if selected_model != current_model:
                success, message = self.ollama.set_model(selected_model)
                if success:
                    print(f"🎯 Using {selected_model} for {task_type.value} task")
                else:
                    print(f"⚠️ Failed to switch to {selected_model}, using {current_model}")
            
            # Build enhanced prompt
            prompt = self._build_enhanced_prompt(user_input, task_type)
            
        except Exception as e:
            print(f"Model delegation failed: {str(e)}")
            prompt = self._build_prompt(user_input)
        
        # Optimize prompt length
        if len(prompt) > 2000:
            prompt = prompt[:2000] + "..."
        
        # Generate response
        if use_streaming:
            self._generate_streaming_response(prompt, user_input, on_response_callback, on_error_callback)
        else:
            self._generate_async_response(prompt, user_input, on_response_callback, on_error_callback)
    
    def _is_actionable_request(self, user_input: str) -> bool:
        """Check if request is actionable for autonomous execution."""
        actionable_keywords = [
            "open", "create", "write", "code", "program", "script", "build", "make",
            "save", "file", "document", "note", "letter", "list",
            "start", "launch", "run", "execute", "search", "find", "browse",
            "screenshot", "capture", "analyze", "look", "see", "show",
            "google", "youtube", "website", "internet"
        ]
        
        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in actionable_keywords)
    
    def _execute_autonomous_request(self, user_input: str) -> bool:
        """Execute an autonomous request."""
        if not self.autonomous_system.is_available():
            return False
        
        try:
            # Determine task type
            _, task_type = self.model_manager.analyze_request(user_input)
            
            # Create task request
            task = TaskRequest(
                description=user_input,
                task_type=task_type.value,
                priority="normal"
            )
            
            # Execute task
            return self.autonomous_system.execute_task(task)
            
        except Exception as e:
            print(f"Autonomous execution failed: {str(e)}")
            return False
    
    def _build_prompt(self, user_input: str) -> str:
        """Build a basic prompt with context."""
        # Get recent conversation history
        recent_messages = session_manager.get_current_context(max_messages=4)
        
        # Build context
        context_parts = []
        if recent_messages:
            for msg in recent_messages:
                if msg["role"] != "system":
                    content_preview = msg['content'][:100]
                    context_parts.append(f"{msg['role'].title()}: {content_preview}...")
        
        # Add current file context if available
        if self.current_file_path and self.current_file_content:
            content_preview = self.current_file_content[:200]
            context_parts.append(f"Current file: {self.current_file_path}")
            context_parts.append(f"Content: {content_preview}...")
        
        # Build final prompt
        prompt_parts = []
        if context_parts:
            prompt_parts.extend(context_parts)
            prompt_parts.append("")
        
        prompt_parts.append(f"User: {user_input}")
        prompt_parts.append("Assistant:")
        
        return "\n".join(prompt_parts)
    
    def _build_enhanced_prompt(self, user_input: str, task_type: TaskType) -> str:
        """Build an enhanced prompt optimized for specific task types."""
        
        # Task-specific instructions
        task_instructions = {
            TaskType.CODING: "You are an expert programmer. Provide clean, well-commented code with explanations. Include error handling and best practices.",
            TaskType.CREATIVE_WRITING: "You are a creative writer. Be imaginative, engaging, and expressive. Use vivid language and create compelling content.",
            TaskType.CONVERSATION: "You are a helpful AI assistant. Provide clear, informative, and engaging responses.",
            TaskType.ANALYSIS: "You are an analytical expert. Provide thorough, well-structured analysis with clear reasoning and evidence.",
            TaskType.REASONING: "You are a logical reasoning expert. Think step-by-step and provide clear, methodical solutions.",
            TaskType.VISION: "You are a vision AI expert. Analyze images carefully and provide detailed, accurate descriptions."
        }
        
        # Get task-specific instruction
        system_prompt = task_instructions.get(task_type, task_instructions[TaskType.CONVERSATION])
        
        # Build context
        context_parts = [system_prompt, ""]
        
        # Add conversation history
        recent_messages = session_manager.get_current_context(max_messages=3)
        if recent_messages:
            for msg in recent_messages:
                if msg["role"] != "system":
                    content_preview = msg['content'][:100]
                    context_parts.append(f"{msg['role'].title()}: {content_preview}...")
        
        # Add current file context
        if self.current_file_path and self.current_file_content:
            content_preview = self.current_file_content[:200]
            context_parts.append(f"Current file: {self.current_file_path}")
            context_parts.append(f"Content: {content_preview}...")
        
        context_parts.extend(["", f"User: {user_input}", "Assistant:"])
        
        return "\n".join(context_parts)
    
    def _generate_streaming_response(self, prompt: str, user_input: str, 
                                   on_response_callback: Callable, on_error_callback: Callable):
        """Generate streaming response."""
        accumulated_response = ""
        
        def on_chunk(chunk):
            nonlocal accumulated_response
            accumulated_response += chunk
        
        def on_complete(full_response):
            self._handle_ai_response(full_response, user_input, on_response_callback)
        
        self.ollama.generate_streaming(prompt, on_chunk, on_complete, on_error_callback)
    
    def _generate_async_response(self, prompt: str, user_input: str,
                                on_response_callback: Callable, on_error_callback: Callable):
        """Generate async response."""
        self.ollama.generate_async(
            prompt,
            lambda response: self._handle_ai_response(response, user_input, on_response_callback),
            on_error_callback,
            optimize_for_speed=True
        )
    
    def _handle_ai_response(self, ai_response: str, user_input: str, callback: Callable):
        """Handle AI response."""
        # Add to history
        self.add_to_history("assistant", ai_response)
        
        # Extract and save code if present
        if self.code_extractor.contains_code(ai_response):
            extracted_code = self.code_extractor.extract_code_blocks(ai_response)
            for i, code_block in enumerate(extracted_code):
                filename = f"extracted_code_{int(time.time())}_{i}.{code_block.get('language', 'txt')}"
                self.file_ops.save_file(filename, code_block['code'])
        
        # Call the callback
        callback(ai_response)
    
    def _handle_pc_command(self, user_input: str, on_response_callback: Callable, on_error_callback: Callable):
        """Handle PC interaction commands."""
        try:
            result = parse_and_execute(user_input)
            response = f"✅ Command executed: {result}"
            self._handle_ai_response(response, user_input, on_response_callback)
        except Exception as e:
            error_msg = f"❌ Error executing PC command: {str(e)}"
            on_error_callback(error_msg)
    
    # Autonomous system integration
    def enable_voice_mode(self, continuous: bool = True) -> bool:
        """Enable voice interaction mode."""
        return self.autonomous_system.enable_voice(continuous)
    
    def disable_voice_mode(self):
        """Disable voice interaction mode."""
        self.autonomous_system.disable_voice()
    
    def set_autonomous_mode(self, mode: str) -> bool:
        """Set autonomous operation mode."""
        try:
            autonomous_mode = AutonomousMode(mode)
            return self.autonomous_system.set_mode(autonomous_mode)
        except ValueError:
            return False
    
    def get_autonomous_status(self) -> Dict[str, Any]:
        """Get autonomous system status."""
        return self.autonomous_system.get_status()
    
    def execute_autonomous_task(self, description: str) -> bool:
        """Execute an autonomous task."""
        return self._execute_autonomous_request(description)
    
    def stop_autonomous_task(self):
        """Stop current autonomous task."""
        self.autonomous_system.stop_current_task()
    
    # Model management integration
    def benchmark_model(self, model_name: str, task_types: List[str] = None,
                       progress_callback: Callable = None) -> List[Any]:
        """Benchmark a model."""
        return self.model_manager.benchmark_model(model_name, task_types, progress_callback)
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get model performance summary."""
        return self.model_manager.get_performance_summary()
    
    def update_delegation_config(self, config: Dict[str, Any]):
        """Update model delegation configuration."""
        if 'mother_model' in config:
            self.config.delegation.mother_model = config['mother_model']
        if 'task_delegation' in config:
            self.config.delegation.task_delegation.update(config['task_delegation'])
        
        self.model_manager.save_delegation_config()
    
    # Event handlers for autonomous system
    def _handle_autonomous_voice_command(self, command: str):
        """Handle voice command from autonomous system."""
        # Process as regular user input but with voice context
        def voice_response_callback(response):
            print(f"🤖 Voice Response: {response}")
            self.autonomous_system.speak(response)
        
        def voice_error_callback(error):
            print(f"❌ Voice Error: {error}")
            self.autonomous_system.speak("Sorry, I encountered an error.")
        
        self.process_user_input(command, voice_response_callback, voice_error_callback)
    
    def _handle_autonomous_task_started(self, task):
        """Handle autonomous task started."""
        print(f"🤖 Autonomous task started: {task.description}")
    
    def _handle_autonomous_task_completed(self, task):
        """Handle autonomous task completed."""
        print(f"✅ Autonomous task completed: {task.description}")
    
    def _handle_autonomous_task_failed(self, task, error):
        """Handle autonomous task failed."""
        print(f"❌ Autonomous task failed: {task.description} - {error}")
    
    # Utility methods
    def add_to_history(self, role: str, content: str):
        """Add message to conversation history."""
        session_manager.add_message(role, content)
    
    def get_recent_history(self, max_messages: int = 10) -> List[Tuple[str, str]]:
        """Get recent conversation history."""
        messages = session_manager.get_current_context(max_messages)
        return [(msg["role"], msg["content"]) for msg in messages if msg["role"] != "system"]
    
    def load_file(self, file_path: str) -> bool:
        """Load a file for context."""
        try:
            content = self.file_ops.read_file(file_path)
            self.current_file_path = file_path
            self.current_file_content = content
            return True
        except Exception as e:
            print(f"Failed to load file: {str(e)}")
            return False
    
    def save_file(self, file_path: str, content: str) -> bool:
        """Save content to file."""
        try:
            self.file_ops.save_file(file_path, content)
            return True
        except Exception as e:
            print(f"Failed to save file: {str(e)}")
            return False

# Global AI assistant instance
ai_assistant = AIAssistant()

def get_ai_assistant() -> AIAssistant:
    """Get the global AI assistant."""
    return ai_assistant
