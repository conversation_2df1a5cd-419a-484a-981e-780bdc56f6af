#!/usr/bin/env python3
"""Consolidated autonomous system combining voice, screen automation, and task execution."""

import time
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

from core.config import get_config

# Import autonomous components with fallback
try:
    from voice_interface import voice_interface
    from screen_automation import screen_automation
    from autonomous_agent import autonomous_agent, Action, ActionType
    AUTONOMOUS_AVAILABLE = True
except ImportError:
    AUTONOMOUS_AVAILABLE = False
    voice_interface = None
    screen_automation = None
    autonomous_agent = None

class AutonomousMode(Enum):
    """Autonomous operation modes."""
    DISABLED = "disabled"
    VOICE_ONLY = "voice_only"
    AUTOMATION_ONLY = "automation_only"
    FULL_AUTONOMOUS = "full_autonomous"

@dataclass
class TaskRequest:
    """Autonomous task request."""
    description: str
    task_type: str
    priority: str = "normal"  # low, normal, high, urgent
    requires_confirmation: bool = False
    timeout: float = 60.0

class AutonomousSystem:
    """Unified autonomous system manager."""
    
    def __init__(self):
        self.config = get_config()
        self.mode = AutonomousMode.DISABLED
        self.is_active = False
        
        # Voice state
        self.voice_enabled = False
        self.voice_listening = False
        
        # Task execution state
        self.current_task: Optional[TaskRequest] = None
        self.task_queue: List[TaskRequest] = []
        
        # Callbacks
        self.on_task_started: Optional[Callable] = None
        self.on_task_completed: Optional[Callable] = None
        self.on_task_failed: Optional[Callable] = None
        self.on_voice_command: Optional[Callable] = None
        self.on_status_change: Optional[Callable] = None
        
        # Initialize if available
        if AUTONOMOUS_AVAILABLE:
            self._initialize_components()
    
    def _initialize_components(self):
        """Initialize autonomous components."""
        try:
            # Configure voice interface
            if voice_interface:
                voice_config = self.config.voice
                voice_interface.configure(
                    wake_word=voice_config.wake_word,
                    language=voice_config.language,
                    timeout=voice_config.timeout,
                    energy_threshold=voice_config.energy_threshold
                )
                
                # Set up callbacks
                voice_interface.on_speech_recognized = self._handle_voice_command
                voice_interface.on_wake_word_detected = self._handle_wake_word
                voice_interface.on_listening_started = self._handle_listening_started
                voice_interface.on_listening_stopped = self._handle_listening_stopped
                voice_interface.on_error = self._handle_voice_error
            
            # Configure screen automation
            if screen_automation:
                automation_config = self.config.automation
                screen_automation.configure(
                    screenshot_interval=automation_config.screenshot_interval,
                    confidence_threshold=automation_config.confidence_threshold,
                    click_delay=automation_config.click_delay,
                    type_delay=automation_config.type_delay,
                    safe_mode=automation_config.safe_mode
                )
                
                screen_automation.on_error = self._handle_automation_error
            
            # Configure autonomous agent
            if autonomous_agent:
                agent_config = self.config.agent
                autonomous_agent.settings.auto_confirm_safe_actions = agent_config.auto_confirm_safe_actions
                autonomous_agent.settings.max_retry_attempts = agent_config.max_retry_attempts
                autonomous_agent.settings.action_delay = agent_config.action_delay
                autonomous_agent.settings.voice_feedback = agent_config.voice_feedback
                autonomous_agent.settings.safety_mode = agent_config.safety_mode
                
                # Set up callbacks
                autonomous_agent.on_task_started = self._handle_agent_task_started
                autonomous_agent.on_task_completed = self._handle_agent_task_completed
                autonomous_agent.on_task_failed = self._handle_agent_task_failed
                autonomous_agent.on_confirmation_required = self._handle_confirmation_required
            
            print("✅ Autonomous system initialized successfully")
            
        except Exception as e:
            print(f"⚠️ Failed to initialize autonomous system: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if autonomous features are available."""
        return AUTONOMOUS_AVAILABLE
    
    def set_mode(self, mode: AutonomousMode) -> bool:
        """Set autonomous operation mode."""
        if not AUTONOMOUS_AVAILABLE:
            return False
        
        try:
            # Stop current operations
            self.stop_all()
            
            self.mode = mode
            
            if mode == AutonomousMode.VOICE_ONLY or mode == AutonomousMode.FULL_AUTONOMOUS:
                self.enable_voice()
            
            if mode == AutonomousMode.AUTOMATION_ONLY or mode == AutonomousMode.FULL_AUTONOMOUS:
                self.is_active = True
            
            self._notify_status_change()
            return True
            
        except Exception as e:
            print(f"Failed to set autonomous mode: {str(e)}")
            return False
    
    def enable_voice(self, continuous: bool = True) -> bool:
        """Enable voice interaction."""
        if not AUTONOMOUS_AVAILABLE or not voice_interface:
            return False
        
        try:
            self.voice_enabled = True
            success = voice_interface.start_listening(continuous=continuous)
            
            if success and self.config.agent.voice_feedback:
                voice_interface.speak("Voice mode activated. Say 'Hey Assistant' to get my attention.")
            
            self._notify_status_change()
            return success
            
        except Exception as e:
            print(f"Failed to enable voice: {str(e)}")
            return False
    
    def disable_voice(self):
        """Disable voice interaction."""
        if not AUTONOMOUS_AVAILABLE or not voice_interface:
            return
        
        try:
            self.voice_enabled = False
            voice_interface.stop_listening_now()
            
            if self.config.agent.voice_feedback:
                voice_interface.speak("Voice mode deactivated.")
            
            self._notify_status_change()
            
        except Exception as e:
            print(f"Failed to disable voice: {str(e)}")
    
    def speak(self, text: str):
        """Speak text using TTS."""
        if AUTONOMOUS_AVAILABLE and voice_interface and self.voice_enabled:
            voice_interface.speak(text)
    
    def execute_task(self, task_request: TaskRequest) -> bool:
        """Execute an autonomous task."""
        if not AUTONOMOUS_AVAILABLE:
            return False
        
        try:
            # Add to queue
            self.task_queue.append(task_request)
            
            # Start execution if not already running
            if not self.current_task:
                return self._execute_next_task()
            
            return True
            
        except Exception as e:
            print(f"Failed to execute task: {str(e)}")
            return False
    
    def _execute_next_task(self) -> bool:
        """Execute the next task in queue."""
        if not self.task_queue:
            return False
        
        task = self.task_queue.pop(0)
        self.current_task = task
        
        if self.on_task_started:
            self.on_task_started(task)
        
        # Determine execution method based on task type
        if task.task_type == "voice_command":
            return self._execute_voice_task(task)
        elif task.task_type == "automation":
            return self._execute_automation_task(task)
        else:
            return self._execute_general_task(task)
    
    def _execute_voice_task(self, task: TaskRequest) -> bool:
        """Execute a voice-based task."""
        try:
            if self.config.agent.voice_feedback:
                self.speak(f"Executing: {task.description}")
            
            # Process voice command
            if self.on_voice_command:
                self.on_voice_command(task.description)
            
            return True
            
        except Exception as e:
            self._handle_task_error(task, str(e))
            return False
    
    def _execute_automation_task(self, task: TaskRequest) -> bool:
        """Execute a screen automation task."""
        try:
            # Parse automation commands from description
            actions = self._parse_automation_commands(task.description)
            
            if not actions:
                return False
            
            # Execute actions using autonomous agent
            if autonomous_agent:
                from autonomous_agent import create_and_execute_custom_task
                return create_and_execute_custom_task(task.description, task.description, actions)
            
            return False
            
        except Exception as e:
            self._handle_task_error(task, str(e))
            return False
    
    def _execute_general_task(self, task: TaskRequest) -> bool:
        """Execute a general autonomous task."""
        try:
            # Use the existing autonomous task execution
            if autonomous_agent:
                # Create actions based on task description
                actions = self._create_actions_from_description(task.description)
                
                if actions:
                    from autonomous_agent import create_and_execute_custom_task
                    return create_and_execute_custom_task(task.description, task.description, actions)
            
            return False
            
        except Exception as e:
            self._handle_task_error(task, str(e))
            return False
    
    def _parse_automation_commands(self, description: str) -> List[Action]:
        """Parse automation commands from description."""
        actions = []
        description_lower = description.lower()
        
        # Simple command parsing
        if "open" in description_lower:
            if "pycharm" in description_lower:
                actions.append(Action(ActionType.OPEN_APP, {"app_name": "pycharm"}))
            elif "notepad" in description_lower:
                actions.append(Action(ActionType.OPEN_APP, {"app_name": "notepad"}))
            elif "browser" in description_lower:
                actions.append(Action(ActionType.OPEN_APP, {"app_name": "browser"}))
        
        if "screenshot" in description_lower:
            actions.append(Action(ActionType.SCREENSHOT, {"save_path": "screenshot.png"}))
        
        if "type" in description_lower or "write" in description_lower:
            # Extract text to type (simplified)
            if "poem" in description_lower:
                actions.append(Action(ActionType.TYPE, {"text": "AI-generated poem content..."}))
            elif "code" in description_lower:
                actions.append(Action(ActionType.TYPE, {"text": "# AI-generated code\nprint('Hello, World!')"}))
        
        return actions
    
    def _create_actions_from_description(self, description: str) -> List[Action]:
        """Create actions from task description."""
        actions = []
        
        # Add speaking action if voice feedback is enabled
        if self.config.agent.voice_feedback:
            actions.append(Action(ActionType.SPEAK, {"text": f"I'll help you with: {description}"}))
        
        # Parse and add specific actions
        actions.extend(self._parse_automation_commands(description))
        
        # Add completion message
        if self.config.agent.voice_feedback:
            actions.append(Action(ActionType.SPEAK, {"text": "Task completed!"}))
        
        return actions
    
    def stop_current_task(self):
        """Stop the currently executing task."""
        if self.current_task and autonomous_agent:
            autonomous_agent.stop_current_task()
        
        self.current_task = None
        self._notify_status_change()
    
    def stop_all(self):
        """Stop all autonomous operations."""
        self.stop_current_task()
        self.task_queue.clear()
        
        if self.voice_enabled:
            self.disable_voice()
        
        self.is_active = False
        self._notify_status_change()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current autonomous system status."""
        return {
            'available': AUTONOMOUS_AVAILABLE,
            'mode': self.mode.value,
            'voice_enabled': self.voice_enabled,
            'voice_listening': self.voice_listening,
            'is_active': self.is_active,
            'current_task': self.current_task.description if self.current_task else None,
            'queue_length': len(self.task_queue),
            'voice_speaking': voice_interface.is_speaking if voice_interface else False
        }
    
    # Event handlers
    def _handle_voice_command(self, command: str):
        """Handle voice command."""
        print(f"🎤 Voice command: {command}")
        
        # Check for system commands
        command_lower = command.lower()
        if "stop" in command_lower or "cancel" in command_lower:
            self.stop_current_task()
            self.speak("Task cancelled")
            return
        
        # Create task request
        task = TaskRequest(
            description=command,
            task_type="voice_command",
            priority="normal"
        )
        
        self.execute_task(task)
    
    def _handle_wake_word(self):
        """Handle wake word detection."""
        print("👂 Wake word detected")
        if self.config.agent.voice_feedback:
            self.speak("Yes, I'm listening.")
    
    def _handle_listening_started(self):
        """Handle listening started."""
        self.voice_listening = True
        print("🎤 Started listening...")
        self._notify_status_change()
    
    def _handle_listening_stopped(self):
        """Handle listening stopped."""
        self.voice_listening = False
        print("🔇 Stopped listening")
        self._notify_status_change()
    
    def _handle_voice_error(self, error: str):
        """Handle voice error."""
        print(f"🎤 Voice Error: {error}")
    
    def _handle_automation_error(self, error: str):
        """Handle automation error."""
        print(f"🖥️ Automation Error: {error}")
    
    def _handle_agent_task_started(self, task):
        """Handle agent task started."""
        print(f"🤖 Agent task started: {task.name}")
    
    def _handle_agent_task_completed(self, task):
        """Handle agent task completed."""
        print(f"✅ Agent task completed: {task.name}")
        self._complete_current_task()
    
    def _handle_agent_task_failed(self, task, error):
        """Handle agent task failed."""
        print(f"❌ Agent task failed: {task.name} - {error}")
        self._handle_task_error(self.current_task, error)
    
    def _handle_confirmation_required(self, action) -> bool:
        """Handle confirmation required."""
        if self.config.agent.voice_feedback:
            self.speak(f"Do you want me to {action.description}? Say yes or no.")
        
        # For now, auto-confirm based on safety settings
        return self.config.agent.auto_confirm_safe_actions
    
    def _complete_current_task(self):
        """Complete the current task and start next."""
        if self.current_task:
            if self.on_task_completed:
                self.on_task_completed(self.current_task)
            
            self.current_task = None
        
        # Execute next task if available
        if self.task_queue:
            self._execute_next_task()
        
        self._notify_status_change()
    
    def _handle_task_error(self, task: TaskRequest, error: str):
        """Handle task execution error."""
        if self.on_task_failed:
            self.on_task_failed(task, error)
        
        if self.config.agent.voice_feedback:
            self.speak(f"Task failed: {error}")
        
        self.current_task = None
        
        # Continue with next task
        if self.task_queue:
            self._execute_next_task()
        
        self._notify_status_change()
    
    def _notify_status_change(self):
        """Notify status change."""
        if self.on_status_change:
            self.on_status_change(self.get_status())

# Global autonomous system instance
autonomous_system = AutonomousSystem()

def get_autonomous_system() -> AutonomousSystem:
    """Get the global autonomous system."""
    return autonomous_system
