#!/usr/bin/env python3
"""Configuration management for the AI Assistant."""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class VoiceConfig:
    """Voice interface configuration."""
    wake_word: str = "hey assistant"
    language: str = "en-US"
    timeout: float = 1.0
    phrase_timeout: float = 0.3
    energy_threshold: int = 300
    voice_id: int = 0
    speech_rate: int = 200
    volume: float = 0.9
    continuous_listening: bool = False
    push_to_talk: bool = False
    auto_adjust_noise: bool = True

@dataclass
class AutomationConfig:
    """Screen automation configuration."""
    screenshot_interval: float = 0.5
    confidence_threshold: float = 0.8
    click_delay: float = 0.1
    type_delay: float = 0.05
    scroll_speed: int = 3
    safe_mode: bool = True

@dataclass
class AgentConfig:
    """Autonomous agent configuration."""
    auto_confirm_safe_actions: bool = True
    max_retry_attempts: int = 3
    action_delay: float = 0.5
    screenshot_on_error: bool = True
    voice_feedback: bool = True
    continuous_monitoring: bool = True
    safety_mode: bool = True

@dataclass
class ModelDelegationConfig:
    """Model delegation configuration."""
    mother_model: str = "Auto-Select"
    task_delegation: Dict[str, Dict[str, str]] = None
    auto_benchmark: bool = True
    performance_threshold: float = 0.8
    
    def __post_init__(self):
        if self.task_delegation is None:
            self.task_delegation = {
                'coding': {'primary': '', 'secondary': '', 'fast': ''},
                'creative_writing': {'primary': '', 'secondary': '', 'fast': ''},
                'conversation': {'primary': '', 'secondary': '', 'fast': ''},
                'analysis': {'primary': '', 'secondary': '', 'fast': ''},
                'reasoning': {'primary': '', 'secondary': '', 'fast': ''},
                'vision': {'primary': '', 'secondary': '', 'fast': ''}
            }

@dataclass
class AppConfig:
    """Main application configuration."""
    voice: VoiceConfig = None
    automation: AutomationConfig = None
    agent: AgentConfig = None
    delegation: ModelDelegationConfig = None
    
    # UI Settings
    theme: str = "dark"
    window_size: str = "1200x800"
    auto_save_chats: bool = True
    max_chat_history: int = 1000
    
    # Performance Settings
    max_concurrent_requests: int = 3
    request_timeout: int = 60
    enable_streaming: bool = True
    
    def __post_init__(self):
        if self.voice is None:
            self.voice = VoiceConfig()
        if self.automation is None:
            self.automation = AutomationConfig()
        if self.agent is None:
            self.agent = AgentConfig()
        if self.delegation is None:
            self.delegation = ModelDelegationConfig()

class ConfigManager:
    """Manages application configuration."""
    
    def __init__(self, config_file: str = "ai_assistant_config.json"):
        self.config_file = config_file
        self.config = AppConfig()
        self.load_config()
    
    def load_config(self) -> bool:
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                # Load each section
                if 'voice' in data:
                    self.config.voice = VoiceConfig(**data['voice'])
                if 'automation' in data:
                    self.config.automation = AutomationConfig(**data['automation'])
                if 'agent' in data:
                    self.config.agent = AgentConfig(**data['agent'])
                if 'delegation' in data:
                    self.config.delegation = ModelDelegationConfig(**data['delegation'])
                
                # Load app settings
                for key in ['theme', 'window_size', 'auto_save_chats', 'max_chat_history',
                           'max_concurrent_requests', 'request_timeout', 'enable_streaming']:
                    if key in data:
                        setattr(self.config, key, data[key])
                
                return True
        except Exception as e:
            print(f"Failed to load config: {str(e)}")
        
        return False
    
    def save_config(self) -> bool:
        """Save configuration to file."""
        try:
            data = {
                'voice': asdict(self.config.voice),
                'automation': asdict(self.config.automation),
                'agent': asdict(self.config.agent),
                'delegation': asdict(self.config.delegation),
                'theme': self.config.theme,
                'window_size': self.config.window_size,
                'auto_save_chats': self.config.auto_save_chats,
                'max_chat_history': self.config.max_chat_history,
                'max_concurrent_requests': self.config.max_concurrent_requests,
                'request_timeout': self.config.request_timeout,
                'enable_streaming': self.config.enable_streaming
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Failed to save config: {str(e)}")
            return False
    
    def get_voice_config(self) -> VoiceConfig:
        """Get voice configuration."""
        return self.config.voice
    
    def get_automation_config(self) -> AutomationConfig:
        """Get automation configuration."""
        return self.config.automation
    
    def get_agent_config(self) -> AgentConfig:
        """Get agent configuration."""
        return self.config.agent
    
    def get_delegation_config(self) -> ModelDelegationConfig:
        """Get delegation configuration."""
        return self.config.delegation
    
    def update_voice_config(self, **kwargs):
        """Update voice configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config.voice, key):
                setattr(self.config.voice, key, value)
        self.save_config()
    
    def update_delegation_config(self, **kwargs):
        """Update delegation configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config.delegation, key):
                setattr(self.config.delegation, key, value)
        self.save_config()
    
    def reset_to_defaults(self):
        """Reset configuration to defaults."""
        self.config = AppConfig()
        self.save_config()

# Global config manager instance
config_manager = ConfigManager()

def get_config() -> AppConfig:
    """Get the global configuration."""
    return config_manager.config

def save_config() -> bool:
    """Save the global configuration."""
    return config_manager.save_config()

def update_voice_config(**kwargs):
    """Update voice configuration."""
    config_manager.update_voice_config(**kwargs)

def update_delegation_config(**kwargs):
    """Update delegation configuration."""
    config_manager.update_delegation_config(**kwargs)
