#!/usr/bin/env python3
"""Unified model management system combining all model-related functionality."""

import json
import time
import threading
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum

import os
from enhanced_ollama_api import EnhancedOllamaAPI
from core.config import get_config

class TaskType(Enum):
    """Types of tasks for model delegation."""
    CODING = "coding"
    CREATIVE_WRITING = "creative_writing"
    CONVERSATION = "conversation"
    ANALYSIS = "analysis"
    REASONING = "reasoning"
    VISION = "vision"
    SYSTEM_AUTOMATION = "system_automation"
    WEB_SEARCH = "web_search"

@dataclass
class ModelPerformance:
    """Model performance metrics."""
    model_name: str
    avg_response_time: float
    avg_quality_score: float
    avg_accuracy_score: float
    avg_creativity_score: float
    avg_coherence_score: float
    best_task_types: List[str]
    total_tests: int
    last_updated: str

@dataclass
class BenchmarkResult:
    """Individual benchmark test result."""
    model_name: str
    task_type: str
    prompt: str
    response: str
    response_time: float
    response_length: int
    quality_score: float
    accuracy_score: float
    creativity_score: float
    coherence_score: float
    timestamp: str

class ModelManager:
    """Unified model management system."""
    
    def __init__(self):
        self.ollama = EnhancedOllamaAPI()
        self.config = get_config()
        
        # Performance data
        self.model_performances: Dict[str, ModelPerformance] = {}
        self.benchmark_results: List[BenchmarkResult] = []
        
        # Delegation rules
        self.delegation_rules = self._initialize_delegation_rules()
        
        # Load existing data
        self.load_performance_data()
        self.load_delegation_config()
    
    def _initialize_delegation_rules(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize smart delegation rules based on model characteristics."""
        return {
            'coding': {
                'primary': ['deepseek-coder:6.7b', 'codellama:7b', 'deepseek-r1:8b'],
                'secondary': ['mistral:7b', 'phi4-mini-reasoning:latest', 'qwen3:4b'],
                'fast': ['phi3:mini', 'deepcoder:1.5b', 'qwen3:0.6b']
            },
            'creative_writing': {
                'primary': ['llama2:7b', 'mistral:7b', 'gemma3:12b-it-qat'],
                'secondary': ['neural-chat:7b', 'gemma3:4b', 'llama3.2:latest'],
                'fast': ['phi3:mini', 'gemma3:1b-it-qat', 'qwen3:0.6b']
            },
            'conversation': {
                'primary': ['neural-chat:7b', 'mistral:7b', 'llama2:7b'],
                'secondary': ['phi3:mini', 'qwen3:4b', 'llama3.2:latest'],
                'fast': ['phi3:mini', 'gemma3:1b-it-qat', 'qwen3:0.6b']
            },
            'analysis': {
                'primary': ['deepseek-r1:8b', 'mistral:7b', 'gemma3:12b-it-qat'],
                'secondary': ['phi4-mini-reasoning:latest', 'neural-chat:7b'],
                'fast': ['phi3:mini', 'qwen3:4b', 'gemma3:1b-it-qat']
            },
            'reasoning': {
                'primary': ['deepseek-r1:8b', 'deepseek-r1:7b', 'phi4-mini-reasoning:latest'],
                'secondary': ['mistral:7b', 'qwen3:latest', 'gemma3:12b-it-qat'],
                'fast': ['phi3:mini', 'qwen3:4b', 'deepseek-r1:1.5b']
            },
            'vision': {
                'primary': ['llava:latest', 'qwen2.5vl:3b'],
                'secondary': ['gemma3n:latest'],
                'fast': ['qwen2.5vl:3b']
            }
        }
    
    def get_available_models(self) -> List[str]:
        """Get list of available models."""
        return self.ollama.get_available_models()
    
    def analyze_request(self, request: str) -> Tuple[TaskType, bool]:
        """Analyze a request to determine task type and speed priority."""
        request_lower = request.lower()
        
        # Determine task type
        if any(word in request_lower for word in [
            'code', 'program', 'script', 'function', 'algorithm', 'debug',
            'python', 'javascript', 'java', 'c++', 'html', 'css', 'sql'
        ]):
            task_type = TaskType.CODING
        elif any(word in request_lower for word in [
            'poem', 'story', 'creative', 'write', 'compose', 'letter',
            'essay', 'article', 'blog', 'narrative', 'fiction'
        ]):
            task_type = TaskType.CREATIVE_WRITING
        elif any(word in request_lower for word in [
            'analyze', 'compare', 'evaluate', 'assess', 'examine',
            'study', 'research', 'investigate', 'review'
        ]):
            task_type = TaskType.ANALYSIS
        elif any(word in request_lower for word in [
            'solve', 'problem', 'puzzle', 'logic', 'reasoning',
            'think', 'figure out', 'work out'
        ]):
            task_type = TaskType.REASONING
        elif any(word in request_lower for word in [
            'image', 'picture', 'photo', 'visual', 'see', 'look'
        ]):
            task_type = TaskType.VISION
        elif any(word in request_lower for word in [
            'open', 'launch', 'start', 'run', 'execute', 'automation'
        ]):
            task_type = TaskType.SYSTEM_AUTOMATION
        elif any(word in request_lower for word in [
            'search', 'google', 'youtube', 'website', 'browse'
        ]):
            task_type = TaskType.WEB_SEARCH
        else:
            task_type = TaskType.CONVERSATION
        
        # Determine speed priority
        prioritize_speed = any(word in request_lower for word in [
            'quick', 'fast', 'rapid', 'immediate', 'urgent', 'asap'
        ])
        
        return task_type, prioritize_speed
    
    def select_best_model(self, task_type: TaskType, prioritize_speed: bool = False) -> str:
        """Select the best model for a task type."""
        # Use performance data if available
        if self.model_performances:
            return self._select_from_performance_data(task_type, prioritize_speed)
        
        # Fallback to rule-based selection
        return self._select_from_rules(task_type, prioritize_speed)
    
    def _select_from_performance_data(self, task_type: TaskType, prioritize_speed: bool) -> str:
        """Select model based on performance data."""
        task_name = task_type.value
        available_models = self.get_available_models()
        
        candidates = []
        for model_name, performance in self.model_performances.items():
            if model_name not in available_models:
                continue
            
            if task_name in performance.best_task_types:
                score = 0
                
                # Task ranking bonus
                task_rank = performance.best_task_types.index(task_name)
                score += (10 - task_rank * 2)
                
                # Performance metrics
                if prioritize_speed:
                    speed_score = max(0, 10 - performance.avg_response_time)
                    score += speed_score * 2
                    score += performance.avg_quality_score
                else:
                    score += performance.avg_quality_score * 2
                    score += performance.avg_accuracy_score * 2
                
                candidates.append((model_name, score))
        
        if candidates:
            candidates.sort(key=lambda x: x[1], reverse=True)
            return candidates[0][0]
        
        return self._select_from_rules(task_type, prioritize_speed)
    
    def _select_from_rules(self, task_type: TaskType, prioritize_speed: bool) -> str:
        """Select model based on delegation rules."""
        task_name = task_type.value
        available_models = self.get_available_models()
        
        if task_name not in self.delegation_rules:
            return self._get_fallback_model()
        
        priority_order = ['fast', 'secondary', 'primary'] if prioritize_speed else ['primary', 'secondary', 'fast']
        
        for priority in priority_order:
            for model in self.delegation_rules[task_name][priority]:
                if model in available_models:
                    return model
        
        return self._get_fallback_model()
    
    def _get_fallback_model(self) -> str:
        """Get fallback model."""
        available_models = self.get_available_models()
        fallback_order = ['phi3:mini', 'mistral:7b', 'llama2:7b', 'neural-chat:7b']
        
        for model in fallback_order:
            if model in available_models:
                return model
        
        return available_models[0] if available_models else 'phi3:mini'
    
    def delegate_request(self, request: str) -> Tuple[str, TaskType]:
        """Delegate a request to the best model."""
        task_type, prioritize_speed = self.analyze_request(request)
        
        # Check for manual override
        delegation_config = self.config.delegation
        if delegation_config.mother_model != "Auto-Select":
            # Use manually configured delegation
            task_config = delegation_config.task_delegation.get(task_type.value, {})
            priority = 'fast' if prioritize_speed else 'primary'
            manual_model = task_config.get(priority, '')
            
            if manual_model and manual_model in self.get_available_models():
                return manual_model, task_type
        
        # Use automatic selection
        selected_model = self.select_best_model(task_type, prioritize_speed)
        return selected_model, task_type
    
    def benchmark_model(self, model_name: str, task_types: List[str] = None, 
                       progress_callback: Callable = None) -> List[BenchmarkResult]:
        """Benchmark a model across specified task types."""
        if task_types is None:
            task_types = ['coding', 'creative_writing', 'conversation', 'analysis']
        
        test_prompts = self._get_test_prompts()
        results = []
        
        total_tests = sum(len(test_prompts.get(task, [])) for task in task_types)
        current_test = 0
        
        for task_type in task_types:
            if task_type not in test_prompts:
                continue
            
            for test_case in test_prompts[task_type][:2]:  # Limit to 2 tests per type
                current_test += 1
                
                if progress_callback:
                    progress_callback(current_test, total_tests, f"Testing {task_type}")
                
                result = self._run_single_benchmark(model_name, task_type, test_case)
                if result:
                    results.append(result)
        
        # Update performance data
        if results:
            self._update_model_performance(model_name, results)
        
        return results
    
    def _get_test_prompts(self) -> Dict[str, List[Dict]]:
        """Get test prompts for benchmarking."""
        return {
            "coding": [
                {
                    "prompt": "Write a Python function to calculate the factorial of a number using recursion.",
                    "expected_keywords": ["def", "factorial", "return", "if", "else"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Create a simple calculator class in Python with basic operations.",
                    "expected_keywords": ["class", "Calculator", "def", "add", "subtract"],
                    "complexity": "medium"
                }
            ],
            "creative_writing": [
                {
                    "prompt": "Write a short poem about artificial intelligence and the future.",
                    "expected_keywords": ["AI", "future", "technology", "digital"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Create a brief story about a robot learning to paint.",
                    "expected_keywords": ["robot", "paint", "art", "learn"],
                    "complexity": "medium"
                }
            ],
            "conversation": [
                {
                    "prompt": "What are the benefits and drawbacks of remote work?",
                    "expected_keywords": ["remote", "work", "benefits", "drawbacks"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Explain quantum computing in simple terms.",
                    "expected_keywords": ["quantum", "computing", "simple", "explain"],
                    "complexity": "hard"
                }
            ],
            "analysis": [
                {
                    "prompt": "Compare the pros and cons of Python vs JavaScript for web development.",
                    "expected_keywords": ["Python", "JavaScript", "web", "pros", "cons"],
                    "complexity": "medium"
                },
                {
                    "prompt": "Analyze the time complexity of bubble sort algorithm.",
                    "expected_keywords": ["bubble", "sort", "time", "complexity"],
                    "complexity": "hard"
                }
            ]
        }
    
    def _run_single_benchmark(self, model_name: str, task_type: str, test_case: Dict) -> Optional[BenchmarkResult]:
        """Run a single benchmark test."""
        try:
            # Switch to model
            success, message = self.ollama.set_model(model_name)
            if not success:
                return None
            
            # Time the response
            start_time = time.time()
            success, response = self.ollama.generate_sync(test_case["prompt"])
            end_time = time.time()
            
            if not success or not response:
                return None
            
            response_time = end_time - start_time
            
            # Score the response
            quality_score, accuracy_score, creativity_score, coherence_score = self._score_response(
                response, test_case["expected_keywords"], task_type
            )
            
            return BenchmarkResult(
                model_name=model_name,
                task_type=task_type,
                prompt=test_case["prompt"],
                response=response[:200] + "..." if len(response) > 200 else response,
                response_time=response_time,
                response_length=len(response),
                quality_score=quality_score,
                accuracy_score=accuracy_score,
                creativity_score=creativity_score,
                coherence_score=coherence_score,
                timestamp=time.time()
            )
            
        except Exception as e:
            print(f"Benchmark error: {str(e)}")
            return None
    
    def _score_response(self, response: str, expected_keywords: List[str], task_type: str) -> Tuple[float, float, float, float]:
        """Score a response for quality, accuracy, creativity, and coherence."""
        response_lower = response.lower()
        
        # Accuracy score based on keyword presence
        keyword_matches = sum(1 for keyword in expected_keywords if keyword.lower() in response_lower)
        accuracy_score = min(10.0, (keyword_matches / len(expected_keywords)) * 10)
        
        # Quality score based on length and structure
        quality_score = min(10.0, len(response) / 50)
        if '\n' in response:
            quality_score += 1.0
        if any(char in response for char in '.,!?'):
            quality_score += 1.0
        
        # Creativity score
        creativity_score = 5.0
        if task_type == "creative_writing":
            if any(word in response_lower for word in ['imagine', 'dream', 'beautiful']):
                creativity_score += 2.0
        
        # Coherence score
        coherence_score = 5.0
        if response.strip().endswith(('.', '!', '?')):
            coherence_score += 2.0
        if not any(word in response_lower for word in ['error', 'sorry', 'cannot']):
            coherence_score += 3.0
        
        return quality_score, accuracy_score, creativity_score, coherence_score
    
    def _update_model_performance(self, model_name: str, results: List[BenchmarkResult]):
        """Update model performance data."""
        if not results:
            return
        
        avg_response_time = sum(r.response_time for r in results) / len(results)
        avg_quality_score = sum(r.quality_score for r in results) / len(results)
        avg_accuracy_score = sum(r.accuracy_score for r in results) / len(results)
        avg_creativity_score = sum(r.creativity_score for r in results) / len(results)
        avg_coherence_score = sum(r.coherence_score for r in results) / len(results)
        
        # Find best task types
        task_scores = {}
        for result in results:
            if result.task_type not in task_scores:
                task_scores[result.task_type] = []
            overall_score = (result.quality_score + result.accuracy_score + 
                           result.creativity_score + result.coherence_score) / 4
            task_scores[result.task_type].append(overall_score)
        
        best_task_types = sorted(
            task_scores.keys(),
            key=lambda x: sum(task_scores[x]) / len(task_scores[x]),
            reverse=True
        )
        
        self.model_performances[model_name] = ModelPerformance(
            model_name=model_name,
            avg_response_time=avg_response_time,
            avg_quality_score=avg_quality_score,
            avg_accuracy_score=avg_accuracy_score,
            avg_creativity_score=avg_creativity_score,
            avg_coherence_score=avg_coherence_score,
            best_task_types=best_task_types,
            total_tests=len(results),
            last_updated=time.time()
        )
        
        self.save_performance_data()
    
    def load_performance_data(self):
        """Load performance data from file."""
        try:
            if os.path.exists('model_performances.json'):
                with open('model_performances.json', 'r') as f:
                    data = json.load(f)
                
                for model_name, perf_data in data.items():
                    self.model_performances[model_name] = ModelPerformance(**perf_data)
        except Exception as e:
            print(f"Failed to load performance data: {str(e)}")
    
    def save_performance_data(self):
        """Save performance data to file."""
        try:
            data = {name: asdict(perf) for name, perf in self.model_performances.items()}
            with open('model_performances.json', 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Failed to save performance data: {str(e)}")
    
    def load_delegation_config(self):
        """Load delegation configuration."""
        try:
            if os.path.exists('delegation_config.json'):
                with open('delegation_config.json', 'r') as f:
                    config_data = json.load(f)
                
                # Update config object
                if 'mother_model' in config_data:
                    self.config.delegation.mother_model = config_data['mother_model']
                if 'task_delegation' in config_data:
                    self.config.delegation.task_delegation.update(config_data['task_delegation'])
        except Exception as e:
            print(f"Failed to load delegation config: {str(e)}")
    
    def save_delegation_config(self):
        """Save delegation configuration."""
        try:
            config_data = {
                'mother_model': self.config.delegation.mother_model,
                'task_delegation': self.config.delegation.task_delegation,
                'timestamp': time.time()
            }
            
            with open('delegation_config.json', 'w') as f:
                json.dump(config_data, f, indent=2)
        except Exception as e:
            print(f"Failed to save delegation config: {str(e)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all models."""
        summary = {}
        for model_name, performance in self.model_performances.items():
            summary[model_name] = {
                'speed': f"{performance.avg_response_time:.2f}s",
                'quality': f"{performance.avg_quality_score:.1f}/10",
                'accuracy': f"{performance.avg_accuracy_score:.1f}/10",
                'best_for': performance.best_task_types[:3]
            }
        return summary

# Global model manager instance
model_manager = ModelManager()

def get_model_manager() -> ModelManager:
    """Get the global model manager."""
    return model_manager
