#!/usr/bin/env python3
"""Test all 24 models to create optimal delegation system."""

import sys
import time
from model_benchmarker import model_benchmarker

def main():
    """Run comprehensive testing of all models."""
    print("🤖 MOTHER MODEL TESTING SYSTEM")
    print("=" * 60)
    print("This will test all your 24 models across different task types")
    print("to create an intelligent delegation system based on:")
    print("• Response time (speed)")
    print("• Response quality")
    print("• Task-specific accuracy")
    print("• Creative capabilities")
    print("• Coherence and structure")
    print()
    
    # Check if we should load existing results
    if model_benchmarker.load_benchmark_results():
        print("\n📊 Existing benchmark results found!")
        model_benchmarker.print_performance_summary()
        
        response = input("\nDo you want to re-run the full benchmark? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Using existing benchmark results.")
            return
    
    print("\n🚀 Starting comprehensive model testing...")
    print("This will take approximately 10-30 minutes depending on your models.")
    print("Each model will be tested on:")
    print("• 2 coding tasks")
    print("• 2 creative writing tasks") 
    print("• 2 problem solving tasks")
    print("• 2 conversation tasks")
    print("• 2 analysis tasks")
    
    response = input("\nProceed with testing? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Testing cancelled.")
        return
    
    # Run the benchmark
    start_time = time.time()
    
    try:
        performances = model_benchmarker.benchmark_all_models(max_tests_per_type=2)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n🎉 Benchmarking completed in {total_time/60:.1f} minutes!")
        print(f"📊 Tested {len(performances)} models")
        
        # Print summary
        model_benchmarker.print_performance_summary()
        
        print("\n" + "="*80)
        print("🎯 MOTHER MODEL DELEGATION SYSTEM READY!")
        print("="*80)
        print("Your AI assistant will now intelligently select the best model")
        print("for each task based on actual performance data.")
        print()
        print("🚀 Next steps:")
        print("1. Run: python enhanced_main.py")
        print("2. Try different types of requests:")
        print("   • 'Write me a poem' → Uses best creative writing model")
        print("   • 'Create a Python calculator' → Uses best coding model")
        print("   • 'Explain quantum computing' → Uses best analysis model")
        print("   • 'Open YouTube and search games' → Uses fastest model")
        print()
        print("The system will automatically choose the optimal model")
        print("for speed vs quality based on your request!")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Testing interrupted by user")
        print("Partial results may have been saved.")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        print("Please check that Ollama is running and models are available.")

if __name__ == "__main__":
    main()
