#!/usr/bin/env python3
"""Dialog windows for the AI Assistant GUI."""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable, List, Dict, Any

class BenchmarkDialog:
    """Dialog for benchmarking models."""
    
    def __init__(self, parent, model_name: str):
        self.parent = parent
        self.model_name = model_name
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Benchmark: {model_name}")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Header
        header = ttk.Label(
            self.dialog,
            text=f"🧪 Benchmarking: {self.model_name}",
            font=('Segoe UI', 14, 'bold')
        )
        header.pack(pady=10)
        
        # Progress
        self.progress_label = ttk.Label(self.dialog, text="Ready to start benchmark")
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(self.dialog, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=20, pady=5)
        
        # Results area
        results_frame = ttk.LabelFrame(self.dialog, text="Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.results_text = tk.Text(
            results_frame,
            height=10,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.start_btn = ttk.Button(
            button_frame,
            text="🚀 Start Benchmark",
            command=self.start_benchmark
        )
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.close_btn = ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.dialog.destroy
        )
        self.close_btn.pack(side=tk.RIGHT, padx=5)
    
    def start_benchmark(self):
        """Start the benchmark process."""
        self.start_btn.config(state=tk.DISABLED)
        self.progress_bar.start()
        self.progress_label.config(text="Benchmarking in progress...")
        
        # Add placeholder results
        self.results_text.config(state=tk.NORMAL)
        self.results_text.insert(tk.END, f"Benchmark Results for {self.model_name}:\n\n")
        self.results_text.insert(tk.END, "⚡ Speed: Testing...\n")
        self.results_text.insert(tk.END, "🎯 Quality: Testing...\n")
        self.results_text.insert(tk.END, "✅ Accuracy: Testing...\n")
        self.results_text.insert(tk.END, "🎨 Creativity: Testing...\n")
        self.results_text.config(state=tk.DISABLED)
        
        # Simulate benchmark completion
        self.dialog.after(3000, self.complete_benchmark)
    
    def complete_benchmark(self):
        """Complete the benchmark."""
        self.progress_bar.stop()
        self.progress_label.config(text="Benchmark completed!")
        self.start_btn.config(state=tk.NORMAL)
        
        # Update results
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"✅ Benchmark Results for {self.model_name}:\n\n")
        self.results_text.insert(tk.END, "⚡ Average Speed: 2.3 seconds\n")
        self.results_text.insert(tk.END, "🎯 Quality Score: 8.5/10\n")
        self.results_text.insert(tk.END, "✅ Accuracy Score: 9.2/10\n")
        self.results_text.insert(tk.END, "🎨 Creativity Score: 7.8/10\n")
        self.results_text.insert(tk.END, "🧠 Coherence Score: 8.9/10\n\n")
        self.results_text.insert(tk.END, "🏅 Best suited for: Conversation, Analysis\n")
        self.results_text.config(state=tk.DISABLED)

class ConfigDialog:
    """Configuration dialog."""
    
    def __init__(self, parent, available_models: List[str]):
        self.parent = parent
        self.available_models = available_models
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🧠 Model Configuration")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create configuration widgets."""
        # Header
        header = ttk.Label(
            self.dialog,
            text="🧠 Model Delegation Configuration",
            font=('Segoe UI', 14, 'bold')
        )
        header.pack(pady=10)
        
        # Notebook for different config sections
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Mother model tab
        mother_frame = ttk.Frame(notebook)
        notebook.add(mother_frame, text="Mother Model")
        self.create_mother_config(mother_frame)
        
        # Task delegation tab
        delegation_frame = ttk.Frame(notebook)
        notebook.add(delegation_frame, text="Task Delegation")
        self.create_delegation_config(delegation_frame)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        save_btn = ttk.Button(
            button_frame,
            text="💾 Save Configuration",
            command=self.save_config
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        close_btn = ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.dialog.destroy
        )
        close_btn.pack(side=tk.RIGHT, padx=5)
    
    def create_mother_config(self, parent):
        """Create mother model configuration."""
        # Description
        desc = ttk.Label(
            parent,
            text="The Mother Model coordinates task delegation to specialized models.",
            wraplength=500
        )
        desc.pack(pady=10)
        
        # Selection
        selection_frame = ttk.LabelFrame(parent, text="Mother Model Selection")
        selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.mother_var = tk.StringVar(value="Auto-Select")
        
        auto_radio = ttk.Radiobutton(
            selection_frame,
            text="🎯 Auto-Select (Recommended)",
            variable=self.mother_var,
            value="Auto-Select"
        )
        auto_radio.pack(anchor=tk.W, padx=10, pady=5)
        
        manual_frame = ttk.Frame(selection_frame)
        manual_frame.pack(fill=tk.X, padx=10, pady=5)
        
        manual_radio = ttk.Radiobutton(
            manual_frame,
            text="🔧 Manual:",
            variable=self.mother_var,
            value="Manual"
        )
        manual_radio.pack(side=tk.LEFT)
        
        self.manual_combo = ttk.Combobox(
            manual_frame,
            values=self.available_models,
            state="readonly"
        )
        self.manual_combo.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_delegation_config(self, parent):
        """Create task delegation configuration."""
        # Scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Task types
        tasks = [
            ("💻 Coding", "Programming and code generation"),
            ("✍️ Creative Writing", "Poems, stories, creative content"),
            ("💬 Conversation", "General chat and Q&A"),
            ("📊 Analysis", "Data analysis and comparisons"),
            ("🧠 Reasoning", "Logic and problem solving"),
            ("👁️ Vision", "Image analysis and description")
        ]
        
        self.task_combos = {}
        
        for task_name, task_desc in tasks:
            task_frame = ttk.LabelFrame(scrollable_frame, text=task_name)
            task_frame.pack(fill=tk.X, padx=10, pady=5)
            
            ttk.Label(task_frame, text=task_desc, font=('Segoe UI', 9)).pack(anchor=tk.W, padx=5, pady=5)
            
            # Model selection for this task
            grid_frame = ttk.Frame(task_frame)
            grid_frame.pack(fill=tk.X, padx=5, pady=5)
            
            priorities = [("🥇 Primary", 0), ("🥈 Secondary", 1), ("⚡ Fast", 2)]
            task_key = task_name.split()[1].lower()
            self.task_combos[task_key] = {}
            
            for priority_name, row in priorities:
                ttk.Label(grid_frame, text=priority_name).grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
                
                combo = ttk.Combobox(
                    grid_frame,
                    values=[""] + self.available_models,
                    state="readonly",
                    width=25
                )
                combo.grid(row=row, column=1, padx=5, pady=2)
                self.task_combos[task_key][priority_name.split()[1].lower()] = combo
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def save_config(self):
        """Save configuration."""
        messagebox.showinfo("Configuration", "Configuration saved successfully!")
        self.dialog.destroy()

class VoiceSettingsDialog:
    """Voice settings dialog."""
    
    def __init__(self, parent):
        self.parent = parent
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🎤 Voice Settings")
        self.dialog.geometry("400x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create voice settings widgets."""
        # Header
        header = ttk.Label(
            self.dialog,
            text="🎤 Voice Configuration",
            font=('Segoe UI', 14, 'bold')
        )
        header.pack(pady=10)
        
        # Wake word settings
        wake_frame = ttk.LabelFrame(self.dialog, text="Wake Word")
        wake_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(wake_frame, text="Wake Word:").pack(anchor=tk.W, padx=5, pady=5)
        self.wake_word_var = tk.StringVar(value="hey assistant")
        wake_entry = ttk.Entry(wake_frame, textvariable=self.wake_word_var)
        wake_entry.pack(fill=tk.X, padx=5, pady=5)
        
        # Voice settings
        voice_frame = ttk.LabelFrame(self.dialog, text="Voice Output")
        voice_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(voice_frame, text="Speech Rate:").pack(anchor=tk.W, padx=5, pady=5)
        self.rate_var = tk.IntVar(value=200)
        rate_scale = ttk.Scale(voice_frame, from_=100, to=300, variable=self.rate_var, orient=tk.HORIZONTAL)
        rate_scale.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(voice_frame, text="Volume:").pack(anchor=tk.W, padx=5, pady=5)
        self.volume_var = tk.DoubleVar(value=0.9)
        volume_scale = ttk.Scale(voice_frame, from_=0.0, to=1.0, variable=self.volume_var, orient=tk.HORIZONTAL)
        volume_scale.pack(fill=tk.X, padx=5, pady=5)
        
        # Recognition settings
        recog_frame = ttk.LabelFrame(self.dialog, text="Speech Recognition")
        recog_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.continuous_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            recog_frame,
            text="Continuous listening",
            variable=self.continuous_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        self.auto_adjust_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            recog_frame,
            text="Auto-adjust for ambient noise",
            variable=self.auto_adjust_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        test_btn = ttk.Button(
            button_frame,
            text="🔊 Test Voice",
            command=self.test_voice
        )
        test_btn.pack(side=tk.LEFT, padx=5)
        
        save_btn = ttk.Button(
            button_frame,
            text="💾 Save",
            command=self.save_settings
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        close_btn = ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.dialog.destroy
        )
        close_btn.pack(side=tk.RIGHT, padx=5)
    
    def test_voice(self):
        """Test voice output."""
        messagebox.showinfo("Test Voice", "Voice test would play here")
    
    def save_settings(self):
        """Save voice settings."""
        messagebox.showinfo("Voice Settings", "Voice settings saved successfully!")
        self.dialog.destroy()
