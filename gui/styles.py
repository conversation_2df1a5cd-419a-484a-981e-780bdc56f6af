#!/usr/bin/env python3
"""GUI styles and theming."""

import tkinter as tk
from tkinter import ttk

# Color schemes
COLORS = {
    'bg': '#2b2b2b',
    'fg': '#ffffff',
    'accent': '#4a9eff',
    'secondary': '#6c757d',
    'success': '#28a745',
    'warning': '#ffc107',
    'error': '#dc3545',
    'button_bg': '#3c3c3c',
    'button_hover': '#4a4a4a',
    'input_bg': '#404040',
    'sidebar_bg': '#333333',
    'chat_bg': '#2b2b2b',
    'user_msg': '#4a9eff',
    'ai_msg': '#28a745',
    'system_msg': '#6c757d'
}

def setup_styles(root):
    """Set up GUI styles and themes."""
    style = ttk.Style()
    
    # Configure theme
    style.theme_use('clam')
    
    # Configure styles
    style.configure('TFrame', background=COLORS['bg'])
    style.configure('TLabel', background=COLORS['bg'], foreground=COLORS['fg'])
    style.configure('TButton', background=COLORS['button_bg'], foreground=COLORS['fg'])
    style.configure('TEntry', background=COLORS['input_bg'], foreground=COLORS['fg'])
    style.configure('TText', background=COLORS['input_bg'], foreground=COLORS['fg'])
    style.configure('TCombobox', background=COLORS['input_bg'], foreground=COLORS['fg'])
    style.configure('TCheckbutton', background=COLORS['bg'], foreground=COLORS['fg'])
    style.configure('TRadiobutton', background=COLORS['bg'], foreground=COLORS['fg'])
    style.configure('TLabelFrame', background=COLORS['bg'], foreground=COLORS['fg'])
    style.configure('TNotebook', background=COLORS['bg'])
    style.configure('TNotebook.Tab', background=COLORS['button_bg'], foreground=COLORS['fg'])
    
    # Configure hover effects
    style.map('TButton',
              background=[('active', COLORS['button_hover']),
                         ('pressed', COLORS['accent'])])
    
    # Configure root window
    root.configure(bg=COLORS['bg'])
    
    # Custom styles
    style.configure('Header.TLabel', font=('Segoe UI', 12, 'bold'))
    style.configure('Sidebar.TLabel', font=('Segoe UI', 9))
    style.configure('Status.TLabel', font=('Segoe UI', 8))
    style.configure('Dark.TCombobox', background=COLORS['input_bg'])
    
    return style
