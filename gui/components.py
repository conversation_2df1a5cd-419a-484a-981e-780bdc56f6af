#!/usr/bin/env python3
"""GUI components for the AI Assistant."""

import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Optional, Callable, List
import time

from gui.styles import COLORS

class ChatArea:
    """Chat area component with message display and input."""
    
    def __init__(self, parent):
        self.parent = parent
        self.on_send: Optional[Callable] = None
        self.on_file_drop: Optional[Callable] = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create chat area widgets."""
        # Chat display
        self.chat_frame = ttk.Frame(self.parent)
        self.chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            wrap=tk.WORD,
            font=('Segoe UI', 10),
            bg=COLORS['chat_bg'],
            fg=COLORS['fg'],
            insertbackground=COLORS['fg'],
            state=tk.DISABLED
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        
        # Configure text tags for different message types
        self.chat_display.tag_configure("user", foreground=COLORS['user_msg'], font=('Segoe UI', 10, 'bold'))
        self.chat_display.tag_configure("ai", foreground=COLORS['ai_msg'])
        self.chat_display.tag_configure("system", foreground=COLORS['system_msg'], font=('Segoe UI', 9, 'italic'))
        self.chat_display.tag_configure("error", foreground=COLORS['error'])
        self.chat_display.tag_configure("timestamp", foreground=COLORS['secondary'], font=('Segoe UI', 8))
        
        # Input area
        self.input_frame = ttk.Frame(self.parent)
        self.input_frame.pack(fill=tk.X)
        
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            wrap=tk.WORD,
            font=('Segoe UI', 10),
            bg=COLORS['input_bg'],
            fg=COLORS['fg'],
            insertbackground=COLORS['fg']
        )
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Send button
        self.send_button = ttk.Button(
            self.input_frame,
            text="Send",
            command=self._on_send_click
        )
        self.send_button.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind events
        self.input_text.bind('<Control-Return>', lambda e: self._on_send_click())
        self.input_text.bind('<Shift-Return>', lambda e: None)  # Allow shift+enter for new line
    
    def _on_send_click(self):
        """Handle send button click."""
        if self.on_send:
            self.on_send()
    
    def add_message(self, sender: str, message: str, message_type: str = "normal"):
        """Add a message to the chat display."""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        timestamp = time.strftime("%H:%M:%S")
        self.chat_display.insert(tk.END, f"[{timestamp}] ", "timestamp")
        
        # Add sender and message
        if message_type == "user":
            self.chat_display.insert(tk.END, f"{sender}: ", "user")
        elif message_type == "ai":
            self.chat_display.insert(tk.END, f"{sender}: ", "ai")
        elif message_type == "system":
            self.chat_display.insert(tk.END, f"{sender}: ", "system")
        elif message_type == "error":
            self.chat_display.insert(tk.END, f"{sender}: ", "error")
        else:
            self.chat_display.insert(tk.END, f"{sender}: ")
        
        self.chat_display.insert(tk.END, f"{message}\n\n")
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def get_input(self) -> str:
        """Get input text."""
        return self.input_text.get("1.0", tk.END).strip()
    
    def clear_input(self):
        """Clear input text."""
        self.input_text.delete("1.0", tk.END)
    
    def clear_messages(self):
        """Clear all messages."""
        self.chat_display.config(state=tk.NORMAL)
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state=tk.DISABLED)
    
    def save_chat(self, file_path: str):
        """Save chat to file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                content = self.chat_display.get("1.0", tk.END)
                f.write(content)
        except Exception as e:
            print(f"Failed to save chat: {str(e)}")

class ModelSelector:
    """Model selection component."""
    
    def __init__(self, parent):
        self.parent = parent
        self.on_model_change: Optional[Callable] = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create model selector widgets."""
        # Current model label
        ttk.Label(self.parent, text="Current Model:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        
        # Model combobox
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(
            self.parent,
            textvariable=self.model_var,
            state="readonly",
            width=30
        )
        self.model_combo.pack(fill=tk.X, padx=5, pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self._on_model_selected)
        
        # Model info
        self.info_label = ttk.Label(
            self.parent,
            text="Select a model to start chatting",
            font=('Segoe UI', 8),
            foreground=COLORS['secondary']
        )
        self.info_label.pack(anchor=tk.W, padx=5, pady=(0, 5))
    
    def _on_model_selected(self, event):
        """Handle model selection."""
        if self.on_model_change:
            selected_model = self.model_var.get()
            self.on_model_change(selected_model)
            self.info_label.config(text=f"Using: {selected_model}")
    
    def update_models(self, models: List[str]):
        """Update available models."""
        self.model_combo['values'] = models
        if models and not self.model_var.get():
            self.model_var.set(models[0])
            if self.on_model_change:
                self.on_model_change(models[0])
    
    def get_selected_model(self) -> str:
        """Get currently selected model."""
        return self.model_var.get()
    
    def set_model(self, model_name: str):
        """Set the selected model."""
        self.model_var.set(model_name)
        self.info_label.config(text=f"Using: {model_name}")

class StatusBar:
    """Status bar component."""
    
    def __init__(self, parent):
        self.parent = parent
        self.create_widgets()
    
    def create_widgets(self):
        """Create status bar widgets."""
        self.status_frame = ttk.Frame(self.parent)
        self.status_frame.pack(fill=tk.X)
        
        # Status label
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready",
            style='Status.TLabel'
        )
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Connection indicator
        self.connection_label = ttk.Label(
            self.status_frame,
            text="🔴 Disconnected",
            style='Status.TLabel'
        )
        self.connection_label.pack(side=tk.RIGHT, padx=5)
    
    def set_status(self, status: str):
        """Set status text."""
        self.status_label.config(text=status)
    
    def set_connection_status(self, connected: bool):
        """Set connection status."""
        if connected:
            self.connection_label.config(text="🟢 Connected")
        else:
            self.connection_label.config(text="🔴 Disconnected")

class ToolBar:
    """Toolbar component with action buttons."""
    
    def __init__(self, parent):
        self.parent = parent
        
        # Callbacks
        self.on_new_chat: Optional[Callable] = None
        self.on_voice_toggle: Optional[Callable] = None
        self.on_autonomous_task: Optional[Callable] = None
        self.on_benchmark: Optional[Callable] = None
        self.on_config: Optional[Callable] = None
        self.on_file_operation: Optional[Callable] = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create toolbar widgets."""
        # Main toolbar frame
        self.toolbar_frame = ttk.Frame(self.parent)
        self.toolbar_frame.pack(fill=tk.X)
        
        # Left side buttons
        left_frame = ttk.Frame(self.toolbar_frame)
        left_frame.pack(side=tk.LEFT)
        
        # New chat button
        self.new_chat_btn = ttk.Button(
            left_frame,
            text="📝 New Chat",
            command=self._on_new_chat_click
        )
        self.new_chat_btn.pack(side=tk.LEFT, padx=2)
        
        # Voice button
        self.voice_btn = ttk.Button(
            left_frame,
            text="🎤 Voice",
            command=self._on_voice_click
        )
        self.voice_btn.pack(side=tk.LEFT, padx=2)
        
        # Autonomous button
        self.auto_btn = ttk.Button(
            left_frame,
            text="🤖 Auto",
            command=self._on_auto_click
        )
        self.auto_btn.pack(side=tk.LEFT, padx=2)
        
        # Right side buttons
        right_frame = ttk.Frame(self.toolbar_frame)
        right_frame.pack(side=tk.RIGHT)
        
        # Benchmark button
        self.benchmark_btn = ttk.Button(
            right_frame,
            text="🧪 Benchmark",
            command=self._on_benchmark_click
        )
        self.benchmark_btn.pack(side=tk.LEFT, padx=2)
        
        # Config button
        self.config_btn = ttk.Button(
            right_frame,
            text="⚙️ Config",
            command=self._on_config_click
        )
        self.config_btn.pack(side=tk.LEFT, padx=2)
    
    def _on_new_chat_click(self):
        if self.on_new_chat:
            self.on_new_chat()
    
    def _on_voice_click(self):
        if self.on_voice_toggle:
            self.on_voice_toggle()
    
    def _on_auto_click(self):
        if self.on_autonomous_task:
            self.on_autonomous_task()
    
    def _on_benchmark_click(self):
        if self.on_benchmark:
            self.on_benchmark()
    
    def _on_config_click(self):
        if self.on_config:
            self.on_config()
    
    def update_voice_status(self, enabled: bool):
        """Update voice button status."""
        if enabled:
            self.voice_btn.config(text="🎤 Voice (On)")
        else:
            self.voice_btn.config(text="🎤 Voice")
    
    def update_auto_status(self, active: bool):
        """Update autonomous button status."""
        if active:
            self.auto_btn.config(text="🤖 Auto (Active)")
        else:
            self.auto_btn.config(text="🤖 Auto")
