#!/usr/bin/env python3
"""Consolidated main GUI window with all features."""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import Optional, Callable, List, Dict, Any

from gui.styles import setup_styles, COLORS
from gui.components import <PERSON>tArea, ModelSelector, StatusBar, ToolBar
from gui.dialogs import BenchmarkDialog, ConfigDialog, VoiceSettingsDialog

class MainWindow:
    """Main application window with all features consolidated."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 AI Assistant - Autonomous & Intelligent")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 600)
        
        # Callbacks - will be set by main application
        self.send_callback: Optional[Callable] = None
        self.model_change_callback: Optional[Callable] = None
        self.new_chat_callback: Optional[Callable] = None
        self.voice_toggle_callback: Optional[Callable] = None
        self.autonomous_task_callback: Optional[Callable] = None
        self.benchmark_model_callback: Optional[Callable] = None
        self.config_callback: Optional[Callable] = None
        self.file_operations_callback: Optional[Callable] = None
        
        # State
        self.current_model = "phi3:mini"
        self.available_models = []
        self.autonomous_status = {}
        self.voice_status = {}
        
        # Setup GUI
        self.setup_gui()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_gui(self):
        """Set up the complete GUI."""
        # Apply styles
        setup_styles(self.root)
        
        # Create main layout
        self.create_layout()
        
        # Create components
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Set up keyboard shortcuts
        self.setup_shortcuts()
    
    def create_layout(self):
        """Create the main layout structure."""
        # Main container
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Toolbar frame
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Content frame
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # Status frame
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
    
    def create_toolbar(self):
        """Create the toolbar with all controls."""
        self.toolbar = ToolBar(self.toolbar_frame)
        
        # Set up toolbar callbacks
        self.toolbar.on_new_chat = self._on_new_chat
        self.toolbar.on_voice_toggle = self._on_voice_toggle
        self.toolbar.on_autonomous_task = self._on_autonomous_task
        self.toolbar.on_benchmark = self._on_benchmark
        self.toolbar.on_config = self._on_config
        self.toolbar.on_file_operation = self._on_file_operation
    
    def create_main_content(self):
        """Create the main content area."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Chat tab
        self.chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chat_frame, text="💬 Chat")
        
        # Create chat area with sidebar
        self.create_chat_area()
        
        # Model Management tab
        self.model_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.model_frame, text="🧠 Models")
        
        # Create model management area
        self.create_model_management()
        
        # Settings tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="⚙️ Settings")
        
        # Create settings area
        self.create_settings_area()
    
    def create_chat_area(self):
        """Create the chat area with sidebar."""
        # Main chat container
        chat_container = ttk.Frame(self.chat_frame)
        chat_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Sidebar
        self.sidebar = ttk.Frame(chat_container, width=250)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        self.sidebar.pack_propagate(False)
        
        # Chat area
        chat_main = ttk.Frame(chat_container)
        chat_main.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Create sidebar content
        self.create_sidebar()
        
        # Create chat area
        self.chat_area = ChatArea(chat_main)
        self.chat_area.on_send = self._on_send_message
        self.chat_area.on_file_drop = self._on_file_drop
    
    def create_sidebar(self):
        """Create the sidebar with model selection and controls."""
        # Model selection
        model_frame = ttk.LabelFrame(self.sidebar, text="🤖 Model Selection")
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.model_selector = ModelSelector(model_frame)
        self.model_selector.on_model_change = self._on_model_change
        
        # Autonomous controls
        auto_frame = ttk.LabelFrame(self.sidebar, text="🤖 Autonomous Mode")
        auto_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.create_autonomous_controls(auto_frame)
        
        # Voice controls
        voice_frame = ttk.LabelFrame(self.sidebar, text="🎤 Voice Control")
        voice_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.create_voice_controls(voice_frame)
        
        # Quick actions
        actions_frame = ttk.LabelFrame(self.sidebar, text="⚡ Quick Actions")
        actions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.create_quick_actions(actions_frame)
    
    def create_autonomous_controls(self, parent):
        """Create autonomous control widgets."""
        # Mode selection
        ttk.Label(parent, text="Mode:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        
        self.auto_mode_var = tk.StringVar(value="disabled")
        modes = [
            ("Disabled", "disabled"),
            ("Voice Only", "voice_only"),
            ("Automation Only", "automation_only"),
            ("Full Autonomous", "full_autonomous")
        ]
        
        for text, value in modes:
            ttk.Radiobutton(
                parent, text=text, variable=self.auto_mode_var,
                value=value, command=self._on_auto_mode_change
            ).pack(anchor=tk.W, padx=15, pady=1)
        
        # Status display
        self.auto_status_label = ttk.Label(parent, text="Status: Disabled")
        self.auto_status_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.auto_stop_btn = ttk.Button(
            button_frame, text="⏹️ Stop Task",
            command=self._on_stop_autonomous, state=tk.DISABLED
        )
        self.auto_stop_btn.pack(fill=tk.X, pady=2)
    
    def create_voice_controls(self, parent):
        """Create voice control widgets."""
        # Voice toggle
        self.voice_enabled_var = tk.BooleanVar()
        voice_toggle = ttk.Checkbutton(
            parent, text="Enable Voice Mode",
            variable=self.voice_enabled_var,
            command=self._on_voice_toggle
        )
        voice_toggle.pack(anchor=tk.W, padx=5, pady=5)
        
        # Voice status
        self.voice_status_label = ttk.Label(parent, text="Status: Disabled")
        self.voice_status_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # Voice settings button
        voice_settings_btn = ttk.Button(
            parent, text="🎛️ Voice Settings",
            command=self._on_voice_settings
        )
        voice_settings_btn.pack(fill=tk.X, padx=5, pady=2)
    
    def create_quick_actions(self, parent):
        """Create quick action buttons."""
        actions = [
            ("📝 New Chat", self._on_new_chat),
            ("📁 Load File", self._on_load_file),
            ("💾 Save Chat", self._on_save_chat),
            ("📊 Performance", self._on_show_performance),
            ("🧪 Benchmark", self._on_benchmark),
            ("⚙️ Settings", self._on_config)
        ]
        
        for text, command in actions:
            btn = ttk.Button(parent, text=text, command=command)
            btn.pack(fill=tk.X, padx=5, pady=2)
    
    def create_model_management(self):
        """Create model management interface."""
        # Model list
        list_frame = ttk.Frame(self.model_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Model tree
        columns = ("Model", "Status", "Best For", "Performance")
        self.model_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        for col in columns:
            self.model_tree.heading(col, text=col)
            self.model_tree.column(col, width=150)
        
        # Scrollbar
        model_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.model_tree.yview)
        self.model_tree.configure(yscrollcommand=model_scrollbar.set)
        
        self.model_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        model_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Model actions
        actions_frame = ttk.Frame(self.model_frame)
        actions_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        model_actions = [
            ("🧪 Benchmark Selected", self._on_benchmark_selected),
            ("⚙️ Configure Delegation", self._on_config_delegation),
            ("📊 View Performance", self._on_view_performance),
            ("🔄 Refresh Models", self._on_refresh_models)
        ]
        
        for text, command in model_actions:
            btn = ttk.Button(actions_frame, text=text, command=command)
            btn.pack(side=tk.LEFT, padx=5)
    
    def create_settings_area(self):
        """Create settings interface."""
        # Settings notebook
        settings_notebook = ttk.Notebook(self.settings_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # General settings
        general_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(general_frame, text="General")
        
        # Voice settings
        voice_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(voice_frame, text="Voice")
        
        # Automation settings
        auto_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(auto_frame, text="Automation")
        
        # Model settings
        model_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(model_frame, text="Models")
        
        # Create settings content
        self.create_general_settings(general_frame)
        self.create_voice_settings(voice_frame)
        self.create_automation_settings(auto_frame)
        self.create_model_settings(model_frame)
    
    def create_general_settings(self, parent):
        """Create general settings."""
        # Theme selection
        theme_frame = ttk.LabelFrame(parent, text="Appearance")
        theme_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(theme_frame, text="Theme:").pack(anchor=tk.W, padx=5, pady=5)
        
        self.theme_var = tk.StringVar(value="dark")
        themes = [("Dark", "dark"), ("Light", "light")]
        
        for text, value in themes:
            ttk.Radiobutton(
                theme_frame, text=text, variable=self.theme_var, value=value
            ).pack(anchor=tk.W, padx=15, pady=2)
        
        # Performance settings
        perf_frame = ttk.LabelFrame(parent, text="Performance")
        perf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.streaming_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            perf_frame, text="Enable streaming responses",
            variable=self.streaming_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        self.auto_save_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            perf_frame, text="Auto-save conversations",
            variable=self.auto_save_var
        ).pack(anchor=tk.W, padx=5, pady=5)
    
    def create_voice_settings(self, parent):
        """Create voice settings."""
        # Voice configuration will be handled by VoiceSettingsDialog
        ttk.Label(
            parent, 
            text="Voice settings are configured through the Voice Settings dialog.\nClick '🎛️ Voice Settings' in the sidebar to configure."
        ).pack(padx=10, pady=20)
    
    def create_automation_settings(self, parent):
        """Create automation settings."""
        # Safety settings
        safety_frame = ttk.LabelFrame(parent, text="Safety")
        safety_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.safe_mode_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            safety_frame, text="Enable safe mode (require confirmation for dangerous actions)",
            variable=self.safe_mode_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        self.auto_confirm_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            safety_frame, text="Auto-confirm safe actions",
            variable=self.auto_confirm_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        # Performance settings
        perf_frame = ttk.LabelFrame(parent, text="Performance")
        perf_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(perf_frame, text="Action delay (seconds):").pack(anchor=tk.W, padx=5, pady=5)
        self.action_delay_var = tk.DoubleVar(value=0.5)
        ttk.Scale(
            perf_frame, from_=0.1, to=2.0, variable=self.action_delay_var,
            orient=tk.HORIZONTAL
        ).pack(fill=tk.X, padx=5, pady=5)
    
    def create_model_settings(self, parent):
        """Create model settings."""
        # Delegation settings
        delegation_frame = ttk.LabelFrame(parent, text="Model Delegation")
        delegation_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.auto_delegate_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            delegation_frame, text="Enable automatic model delegation",
            variable=self.auto_delegate_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        self.auto_benchmark_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            delegation_frame, text="Auto-benchmark new models",
            variable=self.auto_benchmark_var
        ).pack(anchor=tk.W, padx=5, pady=5)
        
        # Mother model selection
        ttk.Label(delegation_frame, text="Mother Model:").pack(anchor=tk.W, padx=5, pady=(10, 0))
        
        self.mother_model_var = tk.StringVar(value="Auto-Select")
        self.mother_model_combo = ttk.Combobox(
            delegation_frame, textvariable=self.mother_model_var,
            state="readonly", width=30
        )
        self.mother_model_combo.pack(anchor=tk.W, padx=5, pady=5)
    
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = StatusBar(self.status_frame)
    
    def setup_shortcuts(self):
        """Set up keyboard shortcuts."""
        self.root.bind('<Control-Return>', lambda e: self._on_send_message())
        self.root.bind('<Control-n>', lambda e: self._on_new_chat())
        self.root.bind('<Control-o>', lambda e: self._on_load_file())
        self.root.bind('<Control-s>', lambda e: self._on_save_chat())
        self.root.bind('<F1>', lambda e: self._on_show_help())
    
    # Event handlers
    def _on_send_message(self):
        """Handle send message."""
        if self.send_callback:
            message = self.chat_area.get_input()
            if message.strip():
                self.send_callback(message)
                self.chat_area.clear_input()
    
    def _on_model_change(self, model_name: str):
        """Handle model change."""
        if self.model_change_callback:
            self.model_change_callback(model_name)
        self.current_model = model_name
    
    def _on_new_chat(self):
        """Handle new chat."""
        if self.new_chat_callback:
            self.new_chat_callback()
        self.chat_area.clear_messages()
    
    def _on_voice_toggle(self):
        """Handle voice toggle."""
        if self.voice_toggle_callback:
            self.voice_toggle_callback()
    
    def _on_autonomous_task(self):
        """Handle autonomous task."""
        if self.autonomous_task_callback:
            # Show task input dialog
            from tkinter import simpledialog
            task = simpledialog.askstring(
                "Autonomous Task",
                "Describe what you'd like me to do:\n\n" +
                "Examples:\n" +
                "• 'Open PyCharm and write a snake game'\n" +
                "• 'Create a poem in notepad'\n" +
                "• 'Search YouTube for Python tutorials'\n" +
                "• 'Take a screenshot and analyze it'\n\n" +
                "Task description:",
                parent=self.root
            )
            
            if task:
                self.autonomous_task_callback(task)
    
    def _on_auto_mode_change(self):
        """Handle autonomous mode change."""
        mode = self.auto_mode_var.get()
        # This would be handled by the main application
        print(f"Autonomous mode changed to: {mode}")
    
    def _on_stop_autonomous(self):
        """Handle stop autonomous task."""
        # This would be handled by the main application
        print("Stop autonomous task requested")
    
    def _on_voice_settings(self):
        """Handle voice settings."""
        VoiceSettingsDialog(self.root)
    
    def _on_benchmark(self):
        """Handle benchmark request."""
        if self.benchmark_model_callback:
            selected_model = self.model_selector.get_selected_model()
            if selected_model:
                self.benchmark_model_callback(selected_model)
    
    def _on_config(self):
        """Handle configuration."""
        if self.config_callback:
            self.config_callback()
    
    def _on_file_operation(self, operation: str):
        """Handle file operations."""
        if self.file_operations_callback:
            self.file_operations_callback(operation)
    
    def _on_file_drop(self, file_path: str):
        """Handle file drop."""
        if self.file_operations_callback:
            self.file_operations_callback(f"load:{file_path}")
    
    def _on_load_file(self):
        """Handle load file."""
        file_path = filedialog.askopenfilename(
            title="Load File",
            filetypes=[
                ("Text files", "*.txt"),
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self._on_file_drop(file_path)
    
    def _on_save_chat(self):
        """Handle save chat."""
        file_path = filedialog.asksaveasfilename(
            title="Save Chat",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.chat_area.save_chat(file_path)
    
    def _on_show_performance(self):
        """Show performance data."""
        # This would show a performance dialog
        messagebox.showinfo("Performance", "Performance data would be shown here")
    
    def _on_benchmark_selected(self):
        """Benchmark selected model."""
        selection = self.model_tree.selection()
        if selection:
            model_name = self.model_tree.item(selection[0])['values'][0]
            if self.benchmark_model_callback:
                self.benchmark_model_callback(model_name)
    
    def _on_config_delegation(self):
        """Configure delegation."""
        if self.config_callback:
            self.config_callback()
    
    def _on_view_performance(self):
        """View performance data."""
        self._on_show_performance()
    
    def _on_refresh_models(self):
        """Refresh model list."""
        # This would refresh the model list
        print("Refreshing models...")
    
    def _on_show_help(self):
        """Show help."""
        help_text = """
🤖 AI Assistant Help

Keyboard Shortcuts:
• Ctrl+Enter: Send message
• Ctrl+N: New chat
• Ctrl+O: Load file
• Ctrl+S: Save chat
• F1: Show this help

Features:
• Intelligent model delegation
• Voice interaction
• Autonomous task execution
• Model benchmarking
• File operations
• Screen automation

For more help, visit the documentation.
        """
        messagebox.showinfo("Help", help_text)
    
    # Public interface methods
    def add_message(self, sender: str, message: str, message_type: str = "normal"):
        """Add a message to the chat area."""
        self.chat_area.add_message(sender, message, message_type)
    
    def update_status(self, status: str):
        """Update status bar."""
        self.status_bar.set_status(status)
    
    def update_models(self, models: List[str]):
        """Update available models."""
        self.available_models = models
        self.model_selector.update_models(models)
        
        # Update mother model combo
        mother_options = ["Auto-Select"] + models
        self.mother_model_combo['values'] = mother_options
        
        # Update model tree
        self.model_tree.delete(*self.model_tree.get_children())
        for model in models:
            self.model_tree.insert("", tk.END, values=(model, "Available", "Unknown", "Not tested"))
    
    def update_autonomous_status(self, status: Dict[str, Any]):
        """Update autonomous status."""
        self.autonomous_status = status
        
        # Update status label
        mode = status.get('mode', 'disabled')
        is_active = status.get('is_active', False)
        current_task = status.get('current_task')
        
        if current_task:
            status_text = f"Status: Active - {current_task}"
            self.auto_stop_btn.config(state=tk.NORMAL)
        elif is_active:
            status_text = f"Status: {mode.replace('_', ' ').title()}"
            self.auto_stop_btn.config(state=tk.DISABLED)
        else:
            status_text = "Status: Disabled"
            self.auto_stop_btn.config(state=tk.DISABLED)
        
        self.auto_status_label.config(text=status_text)
    
    def update_voice_status(self, status: Dict[str, Any]):
        """Update voice status."""
        self.voice_status = status
        
        enabled = status.get('enabled', False)
        listening = status.get('listening', False)
        speaking = status.get('speaking', False)
        
        self.voice_enabled_var.set(enabled)
        
        if speaking:
            status_text = "Status: Speaking"
        elif listening:
            status_text = "Status: Listening"
        elif enabled:
            status_text = "Status: Enabled"
        else:
            status_text = "Status: Disabled"
        
        self.voice_status_label.config(text=status_text)
    
    def show_benchmark_dialog(self, model_name: str):
        """Show benchmark dialog."""
        BenchmarkDialog(self.root, model_name)
    
    def show_config_dialog(self):
        """Show configuration dialog."""
        ConfigDialog(self.root, self.available_models)
    
    def run(self):
        """Run the GUI main loop."""
        self.root.mainloop()
    
    def on_closing(self):
        """Handle window closing."""
        self.root.quit()
        self.root.destroy()
