#!/usr/bin/env python3
"""Model benchmarking dialog for testing individual models."""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, List, Optional, Callable
from model_benchmarker import ModelBenchmarker, BenchmarkResult

class ModelBenchmarkDialog:
    """Dialog for benchmarking individual models."""
    
    def __init__(self, parent, model_name: str, on_complete_callback: Optional[Callable] = None):
        self.parent = parent
        self.model_name = model_name
        self.on_complete_callback = on_complete_callback
        self.benchmarker = ModelBenchmarker()
        self.is_running = False
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create the benchmark dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"Benchmark Model: {self.model_name}")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Header
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        title_label = ttk.Label(
            header_frame,
            text=f"🧪 Benchmarking: {self.model_name}",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack()
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Testing model performance across different task types",
            font=("Segoe UI", 10)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Progress frame
        progress_frame = ttk.Frame(self.dialog)
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_label = ttk.Label(progress_frame, text="Ready to start benchmark")
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='indeterminate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Results frame
        results_frame = ttk.LabelFrame(self.dialog, text="Benchmark Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create treeview for results
        columns = ("Task Type", "Response Time", "Quality", "Accuracy", "Creativity", "Coherence")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=10)
        
        # Configure columns
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == "Task Type":
                self.results_tree.column(col, width=120)
            else:
                self.results_tree.column(col, width=80)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Summary frame
        summary_frame = ttk.LabelFrame(self.dialog, text="Performance Summary")
        summary_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.summary_text = tk.Text(
            summary_frame,
            height=4,
            wrap=tk.WORD,
            font=("Segoe UI", 9),
            state=tk.DISABLED
        )
        self.summary_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Button frame
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(
            button_frame,
            text="🚀 Start Benchmark",
            command=self.start_benchmark
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(
            button_frame,
            text="⏹️ Stop",
            command=self.stop_benchmark,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(
            button_frame,
            text="💾 Save Results",
            command=self.save_results,
            state=tk.DISABLED
        )
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        self.close_button = ttk.Button(
            button_frame,
            text="❌ Close",
            command=self.close_dialog
        )
        self.close_button.pack(side=tk.RIGHT)
        
        # Task selection frame
        task_frame = ttk.LabelFrame(self.dialog, text="Select Tasks to Test")
        task_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.task_vars = {}
        tasks = ["coding", "creative_writing", "conversation", "analysis", "problem_solving"]
        
        for i, task in enumerate(tasks):
            var = tk.BooleanVar(value=True)
            self.task_vars[task] = var
            
            cb = ttk.Checkbutton(
                task_frame,
                text=task.replace("_", " ").title(),
                variable=var
            )
            cb.grid(row=i//3, column=i%3, sticky=tk.W, padx=5, pady=2)
    
    def start_benchmark(self):
        """Start the benchmarking process."""
        if self.is_running:
            return
        
        # Get selected tasks
        selected_tasks = [task for task, var in self.task_vars.items() if var.get()]
        
        if not selected_tasks:
            messagebox.showwarning("No Tasks Selected", "Please select at least one task type to benchmark.")
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        
        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Start benchmark in separate thread
        self.benchmark_thread = threading.Thread(
            target=self._run_benchmark,
            args=(selected_tasks,),
            daemon=True
        )
        self.benchmark_thread.start()
    
    def _run_benchmark(self, selected_tasks: List[str]):
        """Run benchmark in separate thread."""
        try:
            self.dialog.after(0, lambda: self.progress_label.config(text="Initializing benchmark..."))
            
            # Create custom test prompts for selected tasks
            custom_prompts = {}
            for task in selected_tasks:
                if task in self.benchmarker.test_prompts:
                    custom_prompts[task] = self.benchmarker.test_prompts[task][:2]  # Limit to 2 tests per task
            
            # Temporarily replace test prompts
            original_prompts = self.benchmarker.test_prompts
            self.benchmarker.test_prompts = custom_prompts
            
            total_tests = sum(len(prompts) for prompts in custom_prompts.values())
            current_test = 0
            
            results = []
            
            for task_type, prompts in custom_prompts.items():
                if not self.is_running:
                    break
                
                self.dialog.after(0, lambda t=task_type: self.progress_label.config(text=f"Testing {t}..."))
                
                for i, test_case in enumerate(prompts):
                    if not self.is_running:
                        break
                    
                    current_test += 1
                    progress_text = f"Testing {task_type} ({i+1}/{len(prompts)}) - {current_test}/{total_tests}"
                    self.dialog.after(0, lambda t=progress_text: self.progress_label.config(text=t))
                    
                    try:
                        # Run single test
                        result = self._run_single_test(task_type, test_case)
                        if result:
                            results.append(result)
                            # Update UI with result
                            self.dialog.after(0, lambda r=result: self._add_result_to_tree(r))
                    
                    except Exception as e:
                        print(f"Error in test: {str(e)}")
                        continue
            
            # Restore original prompts
            self.benchmarker.test_prompts = original_prompts
            
            # Calculate summary
            if results:
                self.dialog.after(0, lambda: self._update_summary(results))
            
            self.dialog.after(0, self._benchmark_complete)
            
        except Exception as e:
            self.dialog.after(0, lambda: self._benchmark_error(str(e)))
    
    def _run_single_test(self, task_type: str, test_case: Dict) -> Optional[BenchmarkResult]:
        """Run a single benchmark test."""
        try:
            # Switch to the model
            success, message = self.benchmarker.ollama.set_model(self.model_name)
            if not success:
                return None
            
            # Time the response
            start_time = time.time()
            success, response = self.benchmarker.ollama.generate_sync(test_case["prompt"])
            end_time = time.time()
            
            if not success or not response:
                return None
            
            response_time = end_time - start_time
            
            # Score the response
            quality_score, accuracy_score, creativity_score, coherence_score = self.benchmarker._score_response(
                response, test_case["expected_keywords"], task_type, test_case["complexity"]
            )
            
            return BenchmarkResult(
                model_name=self.model_name,
                task_type=task_type,
                prompt=test_case["prompt"],
                response=response[:200] + "..." if len(response) > 200 else response,
                response_time=response_time,
                response_length=len(response),
                quality_score=quality_score,
                accuracy_score=accuracy_score,
                creativity_score=creativity_score,
                coherence_score=coherence_score,
                timestamp=time.time()
            )
            
        except Exception as e:
            print(f"Single test error: {str(e)}")
            return None
    
    def _add_result_to_tree(self, result: BenchmarkResult):
        """Add result to the treeview."""
        self.results_tree.insert("", tk.END, values=(
            result.task_type.replace("_", " ").title(),
            f"{result.response_time:.2f}s",
            f"{result.quality_score:.1f}",
            f"{result.accuracy_score:.1f}",
            f"{result.creativity_score:.1f}",
            f"{result.coherence_score:.1f}"
        ))
    
    def _update_summary(self, results: List[BenchmarkResult]):
        """Update the summary text."""
        if not results:
            return
        
        avg_time = sum(r.response_time for r in results) / len(results)
        avg_quality = sum(r.quality_score for r in results) / len(results)
        avg_accuracy = sum(r.accuracy_score for r in results) / len(results)
        avg_creativity = sum(r.creativity_score for r in results) / len(results)
        avg_coherence = sum(r.coherence_score for r in results) / len(results)
        
        # Find best task type
        task_scores = {}
        for result in results:
            if result.task_type not in task_scores:
                task_scores[result.task_type] = []
            overall_score = (result.quality_score + result.accuracy_score + result.creativity_score + result.coherence_score) / 4
            task_scores[result.task_type].append(overall_score)
        
        best_task = max(task_scores.keys(), key=lambda x: sum(task_scores[x]) / len(task_scores[x])) if task_scores else "None"
        
        summary = f"""📊 Performance Summary for {self.model_name}:
⚡ Average Response Time: {avg_time:.2f} seconds
🎯 Average Quality: {avg_quality:.1f}/10  |  ✅ Accuracy: {avg_accuracy:.1f}/10  |  🎨 Creativity: {avg_creativity:.1f}/10  |  🧠 Coherence: {avg_coherence:.1f}/10
🏅 Best Task Type: {best_task.replace('_', ' ').title()}  |  📝 Total Tests: {len(results)}"""
        
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary)
        self.summary_text.config(state=tk.DISABLED)
    
    def _benchmark_complete(self):
        """Handle benchmark completion."""
        self.is_running = False
        self.progress_bar.stop()
        self.progress_label.config(text="Benchmark completed!")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.NORMAL)
        
        if self.on_complete_callback:
            self.on_complete_callback(self.model_name)
    
    def _benchmark_error(self, error_message: str):
        """Handle benchmark error."""
        self.is_running = False
        self.progress_bar.stop()
        self.progress_label.config(text=f"Error: {error_message}")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        messagebox.showerror("Benchmark Error", f"Benchmark failed: {error_message}")
    
    def stop_benchmark(self):
        """Stop the benchmark."""
        self.is_running = False
        self.progress_bar.stop()
        self.progress_label.config(text="Benchmark stopped")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def save_results(self):
        """Save benchmark results."""
        try:
            # Get results from tree
            results_data = []
            for item in self.results_tree.get_children():
                values = self.results_tree.item(item)['values']
                results_data.append(values)
            
            if not results_data:
                messagebox.showwarning("No Results", "No results to save.")
                return
            
            # Save to file
            import json
            from datetime import datetime
            
            filename = f"benchmark_{self.model_name.replace(':', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            data = {
                'model_name': self.model_name,
                'timestamp': datetime.now().isoformat(),
                'results': results_data,
                'summary': self.summary_text.get(1.0, tk.END).strip()
            }
            
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            
            messagebox.showinfo("Results Saved", f"Benchmark results saved to {filename}")
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save results: {str(e)}")
    
    def close_dialog(self):
        """Close the dialog."""
        if self.is_running:
            if messagebox.askyesno("Benchmark Running", "Benchmark is still running. Stop and close?"):
                self.stop_benchmark()
            else:
                return
        
        self.dialog.destroy()

def show_benchmark_dialog(parent, model_name: str, on_complete_callback: Optional[Callable] = None):
    """Show the benchmark dialog."""
    return ModelBenchmarkDialog(parent, model_name, on_complete_callback)
