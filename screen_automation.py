#!/usr/bin/env python3
"""Advanced screen automation module for real-time screen interaction."""

import time
import threading
from typing import Tuple, Optional, List, Dict, Callable
from dataclasses import dataclass
import logging

# Screen automation dependencies with fallback handling
try:
    import pyautogui
    import cv2
    import numpy as np
    AUTOMATION_AVAILABLE = True
    
    # Configure pyautogui
    pyautogui.FAILSAFE = True  # Move mouse to corner to abort
    pyautogui.PAUSE = 0.1  # Small pause between actions
    
except ImportError:
    AUTOMATION_AVAILABLE = False
    pyautogui = None
    cv2 = None
    np = None


@dataclass
class ScreenRegion:
    """Define a screen region for monitoring or interaction."""
    x: int
    y: int
    width: int
    height: int
    name: str = ""


@dataclass
class AutomationSettings:
    """Screen automation configuration."""
    screenshot_interval: float = 0.5  # Seconds between screenshots
    confidence_threshold: float = 0.8  # Image matching confidence
    click_delay: float = 0.1  # Delay after clicks
    type_delay: float = 0.05  # Delay between keystrokes
    scroll_speed: int = 3  # Scroll wheel speed
    safe_mode: bool = True  # Require confirmation for actions


class ScreenAutomation:
    """Advanced screen automation and monitoring."""
    
    def __init__(self, settings: AutomationSettings = None):
        self.settings = settings or AutomationSettings()
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_monitoring = threading.Event()
        
        # Callbacks
        self.on_screen_change: Optional[Callable[[np.ndarray], None]] = None
        self.on_element_found: Optional[Callable[[str, Tuple[int, int]], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        
        # Screen state
        self.current_screenshot: Optional[np.ndarray] = None
        self.screen_size: Optional[Tuple[int, int]] = None
        
        self._initialize()
    
    def _initialize(self):
        """Initialize screen automation."""
        if not AUTOMATION_AVAILABLE:
            self._handle_error("Screen automation dependencies not available")
            return
        
        try:
            self.screen_size = pyautogui.size()
            self.current_screenshot = self.capture_screen()
        except Exception as e:
            self._handle_error(f"Failed to initialize screen automation: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if screen automation is available."""
        return AUTOMATION_AVAILABLE and self.screen_size is not None
    
    def capture_screen(self, region: ScreenRegion = None) -> Optional[np.ndarray]:
        """Capture screenshot of screen or region."""
        if not self.is_available():
            return None
        
        try:
            if region:
                screenshot = pyautogui.screenshot(region=(region.x, region.y, region.width, region.height))
            else:
                screenshot = pyautogui.screenshot()
            
            # Convert to OpenCV format
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            self.current_screenshot = screenshot_cv
            return screenshot_cv
            
        except Exception as e:
            self._handle_error(f"Failed to capture screen: {str(e)}")
            return None
    
    def find_element_on_screen(self, template_path: str, confidence: float = None) -> Optional[Tuple[int, int]]:
        """Find element on screen using template matching."""
        if not self.is_available():
            return None
        
        confidence = confidence or self.settings.confidence_threshold
        
        try:
            # Capture current screen
            screenshot = self.capture_screen()
            if screenshot is None:
                return None
            
            # Load template
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                self._handle_error(f"Could not load template: {template_path}")
                return None
            
            # Perform template matching
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                # Calculate center of found element
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                if self.on_element_found:
                    self.on_element_found(template_path, (center_x, center_y))
                
                return (center_x, center_y)
            
            return None
            
        except Exception as e:
            self._handle_error(f"Error finding element: {str(e)}")
            return None
    
    def click_at_position(self, x: int, y: int, button: str = 'left', clicks: int = 1) -> bool:
        """Click at specific screen coordinates."""
        if not self.is_available():
            return False
        
        try:
            pyautogui.click(x, y, clicks=clicks, button=button)
            time.sleep(self.settings.click_delay)
            return True
        except Exception as e:
            self._handle_error(f"Failed to click at ({x}, {y}): {str(e)}")
            return False
    
    def click_element(self, template_path: str, confidence: float = None) -> bool:
        """Find and click an element on screen."""
        position = self.find_element_on_screen(template_path, confidence)
        if position:
            return self.click_at_position(position[0], position[1])
        return False
    
    def type_text(self, text: str, interval: float = None) -> bool:
        """Type text with specified interval between characters."""
        if not self.is_available():
            return False
        
        interval = interval or self.settings.type_delay
        
        try:
            pyautogui.typewrite(text, interval=interval)
            return True
        except Exception as e:
            self._handle_error(f"Failed to type text: {str(e)}")
            return False
    
    def press_key(self, key: str, presses: int = 1) -> bool:
        """Press a key or key combination."""
        if not self.is_available():
            return False
        
        try:
            if '+' in key:  # Key combination like 'ctrl+c'
                keys = key.split('+')
                pyautogui.hotkey(*keys)
            else:
                pyautogui.press(key, presses=presses)
            return True
        except Exception as e:
            self._handle_error(f"Failed to press key '{key}': {str(e)}")
            return False
    
    def scroll(self, clicks: int, x: int = None, y: int = None) -> bool:
        """Scroll at position or current mouse position."""
        if not self.is_available():
            return False
        
        try:
            if x is not None and y is not None:
                pyautogui.scroll(clicks, x=x, y=y)
            else:
                pyautogui.scroll(clicks)
            return True
        except Exception as e:
            self._handle_error(f"Failed to scroll: {str(e)}")
            return False
    
    def drag_and_drop(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0) -> bool:
        """Drag from start position to end position."""
        if not self.is_available():
            return False
        
        try:
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button='left')
            return True
        except Exception as e:
            self._handle_error(f"Failed to drag and drop: {str(e)}")
            return False
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        if not self.is_available():
            return (0, 0)
        
        try:
            return pyautogui.position()
        except Exception:
            return (0, 0)
    
    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """Move mouse to position."""
        if not self.is_available():
            return False
        
        try:
            pyautogui.moveTo(x, y, duration=duration)
            return True
        except Exception as e:
            self._handle_error(f"Failed to move mouse: {str(e)}")
            return False
    
    def wait_for_element(self, template_path: str, timeout: float = 10.0, confidence: float = None) -> Optional[Tuple[int, int]]:
        """Wait for element to appear on screen."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            position = self.find_element_on_screen(template_path, confidence)
            if position:
                return position
            time.sleep(0.5)
        
        return None
    
    def start_screen_monitoring(self, callback: Callable[[np.ndarray], None] = None):
        """Start continuous screen monitoring."""
        if not self.is_available():
            return False
        
        if self.monitoring_active:
            return True
        
        self.on_screen_change = callback
        self.stop_monitoring.clear()
        self.monitoring_active = True
        
        self.monitor_thread = threading.Thread(target=self._monitor_screen, daemon=True)
        self.monitor_thread.start()
        
        return True
    
    def stop_screen_monitoring(self):
        """Stop screen monitoring."""
        self.stop_monitoring.set()
        self.monitoring_active = False
    
    def _monitor_screen(self):
        """Monitor screen for changes."""
        last_screenshot = None
        
        while not self.stop_monitoring.is_set() and self.monitoring_active:
            try:
                current_screenshot = self.capture_screen()
                
                if current_screenshot is not None:
                    if last_screenshot is not None and self.on_screen_change:
                        # Check for significant changes
                        diff = cv2.absdiff(current_screenshot, last_screenshot)
                        change_percentage = np.sum(diff > 30) / diff.size
                        
                        if change_percentage > 0.01:  # 1% change threshold
                            self.on_screen_change(current_screenshot)
                    
                    last_screenshot = current_screenshot.copy()
                
                time.sleep(self.settings.screenshot_interval)
                
            except Exception as e:
                self._handle_error(f"Screen monitoring error: {str(e)}")
                break
        
        self.monitoring_active = False
    
    def open_application(self, app_name: str, app_path: str = None) -> bool:
        """Open an application by name or path."""
        try:
            if app_path:
                # Use specific path
                import subprocess
                subprocess.Popen(app_path)
            else:
                # Try to find and open by name
                if app_name.lower() == 'pycharm':
                    # Common PyCharm paths
                    pycharm_paths = [
                        r"C:\Program Files\JetBrains\PyCharm Community Edition*\bin\pycharm64.exe",
                        r"C:\Program Files\JetBrains\PyCharm Professional*\bin\pycharm64.exe",
                        r"C:\Users\<USER>\AppData\Local\JetBrains\PyCharm*\bin\pycharm64.exe"
                    ]
                    
                    import glob
                    for path_pattern in pycharm_paths:
                        matches = glob.glob(path_pattern)
                        if matches:
                            subprocess.Popen(matches[0])
                            return True
                    
                    # Fallback to system command
                    pyautogui.press('win')
                    time.sleep(0.5)
                    pyautogui.typewrite('pycharm')
                    time.sleep(1)
                    pyautogui.press('enter')
                    return True
                else:
                    # Generic application opening
                    pyautogui.press('win')
                    time.sleep(0.5)
                    pyautogui.typewrite(app_name)
                    time.sleep(1)
                    pyautogui.press('enter')
                    return True
            
            return True
            
        except Exception as e:
            self._handle_error(f"Failed to open application '{app_name}': {str(e)}")
            return False
    
    def _handle_error(self, error_message: str):
        """Handle errors."""
        if self.on_error:
            self.on_error(error_message)
        else:
            print(f"Screen Automation Error: {error_message}")


# Global screen automation instance
screen_automation = ScreenAutomation()

def initialize_screen_automation(settings: AutomationSettings = None) -> bool:
    """Initialize screen automation with settings."""
    global screen_automation
    screen_automation = ScreenAutomation(settings)
    return screen_automation.is_available()

def capture_screen(region: ScreenRegion = None) -> Optional[np.ndarray]:
    """Capture screenshot."""
    return screen_automation.capture_screen(region)

def click_at(x: int, y: int, button: str = 'left') -> bool:
    """Click at coordinates."""
    return screen_automation.click_at_position(x, y, button)

def type_text(text: str) -> bool:
    """Type text."""
    return screen_automation.type_text(text)

def press_key(key: str) -> bool:
    """Press key."""
    return screen_automation.press_key(key)

def open_app(app_name: str, app_path: str = None) -> bool:
    """Open application."""
    return screen_automation.open_application(app_name, app_path)
